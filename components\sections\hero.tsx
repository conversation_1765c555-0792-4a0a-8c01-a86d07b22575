"use client";

import { motion } from "framer-motion";
import { ArrowRight, Mail } from "lucide-react";

export default function HeroSection() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="min-h-screen flex items-center pt-20 relative overflow-hidden">
      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 mb-10 md:mb-0">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-2"
            >
              <h2 className="text-xl md:text-2xl font-medium text-gray-600 dark:text-gray-400">
                Hello, I'm
              </h2>
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent pb-2">
                Saurabh Dahariya
              </h1>
              <h3 className="text-2xl md:text-3xl font-semibold">
                Full-Stack Web Developer
              </h3>
              <p className="text-lg md:text-xl max-w-2xl mt-4 text-gray-700 dark:text-gray-300">
                I craft elegant, high-performance web experiences with modern technologies
                and a focus on user experience.
              </p>
              <div className="pt-8 flex flex-col sm:flex-row gap-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => scrollToSection('projects')}
                  className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-lg font-medium rounded-lg flex items-center justify-center gap-2 transition-colors"
                >
                  View My Work
                  <ArrowRight className="h-5 w-5" />
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => scrollToSection('contact')}
                  className="border border-input bg-background hover:bg-accent hover:text-accent-foreground px-8 py-3 text-lg font-medium rounded-lg transition-colors"
                >
                  Contact Me
                </motion.button>
              </div>

              {/* Social Links */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="pt-8 flex gap-4"
              >
                <motion.a
                  href="https://github.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  className="p-3 rounded-full bg-muted hover:bg-accent transition-colors text-sm font-medium"
                >
                  GitHub
                </motion.a>
                <motion.a
                  href="https://linkedin.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  className="p-3 rounded-full bg-muted hover:bg-accent transition-colors text-sm font-medium"
                >
                  LinkedIn
                </motion.a>
                <motion.a
                  href="mailto:<EMAIL>"
                  whileHover={{ scale: 1.1, y: -2 }}
                  className="p-3 rounded-full bg-muted hover:bg-accent transition-colors"
                >
                  <Mail className="h-5 w-5" />
                </motion.a>
              </motion.div>
            </motion.div>
          </div>
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="md:w-1/2 flex justify-center"
          >
            {/* Add your hero image or animation here */}
            <div className="w-64 h-64 md:w-80 md:h-80 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-full flex items-center justify-center">
              <div className="w-56 h-56 md:w-72 md:h-72 bg-background rounded-full flex items-center justify-center">
                <span className="text-6xl">👨‍💻</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
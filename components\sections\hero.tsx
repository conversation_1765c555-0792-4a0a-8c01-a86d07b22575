"use client";

import { motion } from "framer-motion";
import { ArrowR<PERSON>, Mail, Github, Linkedin, ArrowDown } from "lucide-react";
import { useEffect, useRef } from "react";
import { useGSAP } from "@/hooks/useGSAP";
import { revealText, magneticHover } from "@/lib/gsap-animations";

export default function HeroSection() {
  const { scrollTo, fadeIn, slideIn, staggerAnimation } = useGSAP();
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLHeadingElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const socialRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);

  const scrollToSection = (sectionId: string) => {
    scrollTo(`#${sectionId}`, 1.2);
  };

  useEffect(() => {
    // GSAP animations for hero section
    if (titleRef.current) {
      revealText(titleRef.current, {
        duration: 1.2,
        delay: 0.3,
        stagger: 0.1
      });
    }

    if (subtitleRef.current) {
      fadeIn(subtitleRef, {
        delay: 0.5,
        duration: 1,
        from: { opacity: 0, y: 30 }
      });
    }

    if (descriptionRef.current) {
      slideIn(descriptionRef, 'up', {
        delay: 0.8,
        duration: 1
      });
    }

    if (buttonsRef.current) {
      staggerAnimation(buttonsRef.current.children, {
        delay: 1.2,
        stagger: 0.2,
        duration: 0.8
      });
    }

    if (socialRef.current) {
      staggerAnimation(socialRef.current.children, {
        delay: 1.5,
        stagger: 0.1,
        duration: 0.6
      });
    }

    if (imageRef.current) {
      fadeIn(imageRef, {
        delay: 0.6,
        duration: 1.5,
        from: { opacity: 0, scale: 0.8, rotation: 5 },
        to: { opacity: 1, scale: 1, rotation: 0 }
      });
    }

    // Add magnetic hover effect to buttons
    const buttons = buttonsRef.current?.querySelectorAll('button');
    const cleanupFunctions: (() => void)[] = [];

    buttons?.forEach(button => {
      const cleanup = magneticHover(button, 0.2);
      if (cleanup) cleanupFunctions.push(cleanup);
    });

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, []); // Empty dependency array since we only want this to run once

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-background/95">
        {/* Floating Orbs */}
        <div className="absolute top-20 left-20 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '4s'}}></div>

        {/* Grid Pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-background/50 via-transparent to-background/50"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10" ref={heroRef}>
        <div className="text-center space-y-8 max-w-5xl mx-auto">
          {/* Main Heading */}
          <div className="space-y-4">
            <motion.div
              ref={titleRef}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <h1 className="text-7xl md:text-8xl lg:text-9xl font-black bg-gradient-to-r from-primary via-purple-500 to-primary bg-clip-text text-transparent leading-none tracking-tight">
                SAURABH
              </h1>
              <h1 className="text-7xl md:text-8xl lg:text-9xl font-black bg-gradient-to-r from-purple-500 via-primary to-purple-500 bg-clip-text text-transparent leading-none tracking-tight">
                DAHARIYA
              </h1>
            </motion.div>

            <motion.div
              ref={subtitleRef}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              className="space-y-3"
            >
              <h2 className="text-3xl md:text-5xl lg:text-6xl font-bold text-foreground/90">
                Full-Stack Developer
              </h2>
              <div className="flex items-center justify-center gap-4 text-xl md:text-2xl text-muted-foreground">
                <span className="flex items-center gap-2">
                  <span className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></span>
                  Available for opportunities
                </span>
              </div>
            </motion.div>
          </div>

          {/* Description */}
          <motion.div
            ref={descriptionRef}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
            className="space-y-6"
          >
            <p className="text-xl md:text-2xl lg:text-3xl max-w-4xl mx-auto text-muted-foreground leading-relaxed">
              Building <span className="text-primary font-bold">AI-powered web applications</span> with
              React, Node.js, and OpenAI API. Transforming ideas into intelligent digital solutions.
            </p>

            {/* Tech Stack Pills */}
            <div className="flex flex-wrap justify-center gap-3 max-w-3xl mx-auto">
              {['React', 'Node.js', 'OpenAI API', 'MongoDB', 'TypeScript', 'Next.js'].map((tech, index) => (
                <motion.span
                  key={tech}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                  className="px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-sm font-medium text-primary"
                >
                  {tech}
                </motion.span>
              ))}
            </div>
          </motion.div>
          {/* Action Buttons */}
          <motion.div
            ref={buttonsRef}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <motion.button
              onClick={() => scrollToSection('projects')}
              whileHover={{
                scale: 1.05,
                boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
                y: -2
              }}
              whileTap={{ scale: 0.95 }}
              className="group bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 text-white px-10 py-5 rounded-2xl font-bold text-lg transition-all flex items-center justify-center gap-3 shadow-2xl min-w-[200px]"
            >
              <span>View My Work</span>
              <ArrowRight className="h-6 w-6 group-hover:translate-x-1 transition-transform" />
            </motion.button>

            <motion.button
              onClick={() => scrollToSection('contact')}
              whileHover={{
                scale: 1.05,
                y: -2
              }}
              whileTap={{ scale: 0.95 }}
              className="group border-2 border-primary/30 bg-background/80 backdrop-blur-sm hover:bg-primary/10 hover:border-primary/50 px-10 py-5 rounded-2xl font-bold text-lg transition-all flex items-center justify-center gap-3 min-w-[200px]"
            >
              <Mail className="h-6 w-6 group-hover:scale-110 transition-transform" />
              <span>Let's Connect</span>
            </motion.button>
          </motion.div>

          {/* Social Links & Stats */}
          <motion.div
            ref={socialRef}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0, ease: "easeOut" }}
            className="space-y-6"
          >
            {/* Quick Stats */}
            <div className="flex flex-wrap justify-center gap-8 text-lg text-muted-foreground">
              <div className="flex items-center gap-3">
                <span className="text-2xl">🚀</span>
                <span>3+ AI Projects</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-2xl">⚡</span>
                <span>MERN Stack</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-2xl">🎯</span>
                <span>Bengaluru Based</span>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex justify-center gap-6">
              <motion.a
                href="https://github.com/saurabhdahariya"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.1, y: -3 }}
                whileTap={{ scale: 0.95 }}
                className="p-4 bg-primary/10 hover:bg-primary/20 border border-primary/20 hover:border-primary/40 rounded-2xl transition-all group"
              >
                <Github className="h-6 w-6 text-primary group-hover:scale-110 transition-transform" />
              </motion.a>
              <motion.a
                href="https://linkedin.com/in/saurabhdahariya"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.1, y: -3 }}
                whileTap={{ scale: 0.95 }}
                className="p-4 bg-primary/10 hover:bg-primary/20 border border-primary/20 hover:border-primary/40 rounded-2xl transition-all group"
              >
                <Linkedin className="h-6 w-6 text-primary group-hover:scale-110 transition-transform" />
              </motion.a>
              <motion.a
                href="mailto:<EMAIL>"
                whileHover={{ scale: 1.1, y: -3 }}
                whileTap={{ scale: 0.95 }}
                className="p-4 bg-primary/10 hover:bg-primary/20 border border-primary/20 hover:border-primary/40 rounded-2xl transition-all group"
              >
                <Mail className="h-6 w-6 text-primary group-hover:scale-110 transition-transform" />
              </motion.a>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          className="flex flex-col items-center gap-2 text-muted-foreground"
        >
          <span className="text-sm font-medium">Scroll to explore</span>
          <ArrowDown className="h-5 w-5" />
        </motion.div>
      </motion.div>
    </section>
  );
}
"use client";

import { motion } from "framer-motion";
import { ArrowR<PERSON>, Mail, Github, Linkedin, ArrowDown } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useGSAP } from "@/hooks/useGSAP";
import { useTypewriter } from "@/hooks/useTypewriter";

export default function HeroSection() {
  const { scrollTo } = useGSAP();
  const heroRef = useRef<HTMLDivElement>(null);

  // Auto-typing effect for roles
  const { text: typedRole } = useTypewriter({
    words: [
      "Full-Stack Developer",
      "AI Enthusiast",
      "React Specialist",
      "MERN Stack Expert",
      "Problem Solver"
    ],
    typeSpeed: 100,
    deleteSpeed: 50,
    delaySpeed: 2000,
  });

  const scrollToSection = (sectionId: string) => {
    scrollTo(`#${sectionId}`, 1.2);
  };

  // Using Framer Motion for animations instead of GSAP

  return (
    <section id="home" className="min-h-screen flex items-center justify-center py-20">
      <div className="container mx-auto px-4 relative z-10" ref={heroRef}>
        <div className="text-center space-y-12 max-w-6xl mx-auto">

          {/* Main Name */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="space-y-4"
          >
            <h1 className="text-8xl md:text-9xl lg:text-[12rem] font-black bg-gradient-to-r from-primary via-blue-500 to-purple-600 bg-clip-text text-transparent leading-none tracking-tighter font-mono">
              SAURABH
            </h1>
            <h1 className="text-8xl md:text-9xl lg:text-[12rem] font-black bg-gradient-to-r from-purple-600 via-pink-500 to-primary bg-clip-text text-transparent leading-none tracking-tighter font-mono">
              DAHARIYA
            </h1>
          </motion.div>

          {/* Auto-typing Role */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            className="space-y-4"
          >
            <div className="text-3xl md:text-5xl lg:text-6xl font-bold text-foreground/90 font-sans">
              <span className="text-muted-foreground">I'm a </span>
              <span className="text-primary">{typedRole}</span>
              <span className="animate-pulse text-primary">|</span>
            </div>

            <div className="flex items-center justify-center gap-6 text-lg md:text-xl text-muted-foreground">
              <span className="flex items-center gap-2">
                <span className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></span>
                Available for opportunities
              </span>
              <span>•</span>
              <span>📍 Bengaluru, India</span>
            </div>
          </motion.div>

          {/* Description */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
            className="space-y-8"
          >
            <p className="text-xl md:text-2xl max-w-4xl mx-auto text-muted-foreground leading-relaxed font-light">
              Crafting <span className="text-primary font-semibold">intelligent web applications</span> that solve real-world problems.
              Specializing in AI integration, modern React development, and scalable backend solutions.
            </p>

            {/* Tech Stack */}
            <div className="flex flex-wrap justify-center gap-3 max-w-4xl mx-auto">
              {['React', 'Node.js', 'OpenAI API', 'MongoDB', 'TypeScript', 'Next.js', 'Firebase', 'Tailwind CSS'].map((tech, index) => (
                <motion.span
                  key={tech}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                  className="px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-sm font-medium text-primary hover:bg-primary/20 transition-colors"
                >
                  {tech}
                </motion.span>
              ))}
            </div>
          </motion.div>
          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0, ease: "easeOut" }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <motion.button
              onClick={() => scrollToSection('projects')}
              whileHover={{
                scale: 1.05,
                boxShadow: "0 25px 50px rgba(0,0,0,0.15)",
                y: -3
              }}
              whileTap={{ scale: 0.95 }}
              className="group bg-gradient-to-r from-primary via-blue-500 to-purple-600 hover:from-primary/90 hover:via-blue-500/90 hover:to-purple-600/90 text-white px-12 py-4 rounded-2xl font-bold text-lg transition-all flex items-center justify-center gap-3 shadow-2xl min-w-[220px]"
            >
              <span>View My Projects</span>
              <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </motion.button>

            <motion.button
              onClick={() => scrollToSection('contact')}
              whileHover={{
                scale: 1.05,
                y: -3
              }}
              whileTap={{ scale: 0.95 }}
              className="group border-2 border-primary/30 bg-background/80 backdrop-blur-sm hover:bg-primary/10 hover:border-primary/50 px-12 py-4 rounded-2xl font-bold text-lg transition-all flex items-center justify-center gap-3 min-w-[220px]"
            >
              <Mail className="h-5 w-5 group-hover:scale-110 transition-transform" />
              <span>Let's Connect</span>
            </motion.button>
          </motion.div>

          {/* Social Links */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2, ease: "easeOut" }}
            className="flex justify-center gap-6"
          >
            <motion.a
              href="https://github.com/saurabhdahariya"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.1, y: -3 }}
              whileTap={{ scale: 0.95 }}
              className="p-4 bg-primary/10 hover:bg-primary/20 border border-primary/20 hover:border-primary/40 rounded-2xl transition-all group"
            >
              <Github className="h-6 w-6 text-primary group-hover:scale-110 transition-transform" />
            </motion.a>
            <motion.a
              href="https://linkedin.com/in/saurabhdahariya"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.1, y: -3 }}
              whileTap={{ scale: 0.95 }}
              className="p-4 bg-primary/10 hover:bg-primary/20 border border-primary/20 hover:border-primary/40 rounded-2xl transition-all group"
            >
              <Linkedin className="h-6 w-6 text-primary group-hover:scale-110 transition-transform" />
            </motion.a>
            <motion.a
              href="mailto:<EMAIL>"
              whileHover={{ scale: 1.1, y: -3 }}
              whileTap={{ scale: 0.95 }}
              className="p-4 bg-primary/10 hover:bg-primary/20 border border-primary/20 hover:border-primary/40 rounded-2xl transition-all group"
            >
              <Mail className="h-6 w-6 text-primary group-hover:scale-110 transition-transform" />
            </motion.a>
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1.5 }}
            className="pt-12"
          >
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              className="flex flex-col items-center gap-2 text-muted-foreground"
            >
              <span className="text-sm font-medium">Scroll to explore</span>
              <ArrowDown className="h-5 w-5" />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
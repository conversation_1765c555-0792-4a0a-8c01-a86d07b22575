"use client";

import { motion } from "framer-motion";
import { ArrowRight, Mail, Github, Linkedin, ArrowDown, Code, Terminal, Cpu, Zap } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useGSAP } from "@/hooks/useGSAP";
import { useTypewriter } from "@/hooks/useTypewriter";
import { ThreeDBackground } from "@/components/3d-background";

export default function HeroSection() {
  const { scrollTo } = useGSAP();
  const heroRef = useRef<HTMLDivElement>(null);

  // Auto-typing effect for developer roles
  const { text: typedRole } = useTypewriter({
    words: [
      "Full-Stack Developer",
      "AI Engineer",
      "React Architect",
      "Code Craftsman",
      "Tech Innovator",
      "Digital Builder"
    ],
    typeSpeed: 80,
    deleteSpeed: 40,
    delaySpeed: 2500,
  });

  const scrollToSection = (sectionId: string) => {
    scrollTo(`#${sectionId}`, 1.2);
  };

  // Using Framer Motion for animations instead of GSAP

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* 3D Background */}
      <ThreeDBackground />

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-background/90 via-background/70 to-background/90 z-0"></div>

      <div className="container mx-auto px-4 relative z-10" ref={heroRef}>
        <div className="grid lg:grid-cols-2 gap-12 items-center max-w-7xl mx-auto">

          {/* Left Side - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="space-y-8"
          >
            {/* Developer Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium"
            >
              <Code className="h-4 w-4" />
              <span>Full-Stack Developer</span>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            </motion.div>

            {/* Main Heading */}
            <div className="space-y-4">
              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight"
              >
                <span className="text-foreground">Hi, I'm </span>
                <span className="bg-gradient-to-r from-primary via-blue-500 to-purple-600 bg-clip-text text-transparent">
                  Saurabh
                </span>
                <br />
                <span className="bg-gradient-to-r from-purple-600 via-pink-500 to-primary bg-clip-text text-transparent">
                  Dahariya
                </span>
              </motion.h1>

              {/* Auto-typing Role */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="text-xl md:text-3xl font-semibold text-muted-foreground"
              >
                <span className="text-primary font-mono">&gt; </span>
                <span>{typedRole}</span>
                <span className="animate-pulse text-primary">_</span>
              </motion.div>
            </div>

            {/* Description */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-2xl"
            >
              I craft <span className="text-primary font-semibold">intelligent web applications</span> using
              cutting-edge technologies. Passionate about AI integration, clean code, and building
              solutions that make a difference.
            </motion.p>

            {/* Tech Stack */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.9 }}
              className="space-y-4"
            >
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Terminal className="h-4 w-4" />
                <span>Tech Stack</span>
              </div>
              <div className="flex flex-wrap gap-3">
                {[
                  { name: 'React', icon: '⚛️' },
                  { name: 'Node.js', icon: '🟢' },
                  { name: 'TypeScript', icon: '🔷' },
                  { name: 'OpenAI', icon: '🤖' },
                  { name: 'MongoDB', icon: '🍃' },
                  { name: 'Next.js', icon: '▲' }
                ].map((tech, index) => (
                  <motion.div
                    key={tech.name}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 1.1 + index * 0.1 }}
                    className="flex items-center gap-2 px-3 py-2 bg-muted/50 border border-border/50 rounded-lg text-sm font-medium hover:bg-primary/10 hover:border-primary/20 transition-all cursor-default"
                  >
                    <span>{tech.icon}</span>
                    <span>{tech.name}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.3 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <motion.button
                onClick={() => scrollToSection('projects')}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="group bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 text-white px-8 py-4 rounded-xl font-semibold transition-all flex items-center justify-center gap-3 shadow-lg"
              >
                <Code className="h-5 w-5" />
                <span>View My Work</span>
                <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </motion.button>

              <motion.button
                onClick={() => scrollToSection('contact')}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="group border-2 border-primary/30 bg-background/80 backdrop-blur-sm hover:bg-primary/10 hover:border-primary/50 px-8 py-4 rounded-xl font-semibold transition-all flex items-center justify-center gap-3"
              >
                <Mail className="h-5 w-5 group-hover:scale-110 transition-transform" />
                <span>Let's Connect</span>
              </motion.button>
            </motion.div>

            {/* Social Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.5 }}
              className="flex gap-4"
            >
              {[
                { icon: Github, href: "https://github.com/saurabhdahariya", label: "GitHub" },
                { icon: Linkedin, href: "https://linkedin.com/in/saurabhdahariya", label: "LinkedIn" },
                { icon: Mail, href: "mailto:<EMAIL>", label: "Email" }
              ].map((social, index) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  target={social.label !== "Email" ? "_blank" : undefined}
                  rel={social.label !== "Email" ? "noopener noreferrer" : undefined}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-3 bg-muted/50 hover:bg-primary/10 border border-border/50 hover:border-primary/30 rounded-xl transition-all group"
                  title={social.label}
                >
                  <social.icon className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors" />
                </motion.a>
              ))}
            </motion.div>
          </motion.div>
          {/* Right Side - Developer Illustration & Stats */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
            className="relative flex flex-col items-center space-y-8"
          >
            {/* Developer Avatar/Illustration */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.6 }}
              className="relative"
            >
              {/* Main Circle with Glow */}
              <div className="relative w-80 h-80 lg:w-96 lg:h-96">
                {/* Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-purple-500/20 rounded-full blur-3xl animate-pulse"></div>

                {/* Main Container */}
                <div className="relative w-full h-full bg-gradient-to-br from-muted/50 to-background/50 backdrop-blur-sm border border-border/50 rounded-full flex items-center justify-center overflow-hidden">
                  {/* Developer Icon */}
                  <div className="text-8xl lg:text-9xl">👨‍💻</div>

                  {/* Floating Tech Icons */}
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="absolute inset-0"
                  >
                    {[
                      { icon: '⚛️', position: 'top-8 left-1/2 transform -translate-x-1/2' },
                      { icon: '🟢', position: 'right-8 top-1/2 transform -translate-y-1/2' },
                      { icon: '🔷', position: 'bottom-8 left-1/2 transform -translate-x-1/2' },
                      { icon: '🤖', position: 'left-8 top-1/2 transform -translate-y-1/2' },
                    ].map((item, index) => (
                      <motion.div
                        key={index}
                        className={`absolute ${item.position} w-12 h-12 bg-background/80 backdrop-blur-sm border border-border/50 rounded-xl flex items-center justify-center text-xl`}
                        animate={{ rotate: -360 }}
                        transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      >
                        {item.icon}
                      </motion.div>
                    ))}
                  </motion.div>
                </div>
              </div>
            </motion.div>

            {/* Developer Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
              className="grid grid-cols-2 gap-4 w-full max-w-sm"
            >
              {[
                { icon: Code, label: "Projects", value: "10+" },
                { icon: Zap, label: "Experience", value: "2+ Years" },
                { icon: Cpu, label: "Technologies", value: "15+" },
                { icon: Terminal, label: "Commits", value: "500+" }
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 1.2 + index * 0.1 }}
                  className="bg-muted/50 backdrop-blur-sm border border-border/50 rounded-xl p-4 text-center hover:bg-primary/5 hover:border-primary/20 transition-all"
                >
                  <stat.icon className="h-6 w-6 text-primary mx-auto mb-2" />
                  <div className="text-lg font-bold text-foreground">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>

            {/* Status Indicator */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.6 }}
              className="flex items-center gap-3 px-4 py-2 bg-green-500/10 border border-green-500/20 rounded-full text-green-600"
            >
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">Available for opportunities</span>
            </motion.div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 2.0 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="flex flex-col items-center gap-2 text-muted-foreground"
          >
            <span className="text-sm">Scroll to explore</span>
            <ArrowDown className="h-4 w-4" />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
"use client";

import { motion } from "framer-motion";
import { ArrowRight, Mail } from "lucide-react";
import { useEffect, useRef } from "react";
import { useGSAP } from "@/hooks/useGSAP";
import { revealText, magneticHover } from "@/lib/gsap-animations";

export default function HeroSection() {
  const { scrollTo, fadeIn, slideIn, staggerAnimation } = useGSAP();
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLHeadingElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const socialRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);

  const scrollToSection = (sectionId: string) => {
    scrollTo(`#${sectionId}`, 1.2);
  };

  useEffect(() => {
    // GSAP animations for hero section
    if (titleRef.current) {
      revealText(titleRef.current, {
        duration: 1.2,
        delay: 0.3,
        stagger: 0.1
      });
    }

    if (subtitleRef.current) {
      fadeIn(subtitleRef, {
        delay: 0.5,
        duration: 1,
        from: { opacity: 0, y: 30 }
      });
    }

    if (descriptionRef.current) {
      slideIn(descriptionRef, 'up', {
        delay: 0.8,
        duration: 1
      });
    }

    if (buttonsRef.current) {
      staggerAnimation(buttonsRef.current.children, {
        delay: 1.2,
        stagger: 0.2,
        duration: 0.8
      });
    }

    if (socialRef.current) {
      staggerAnimation(socialRef.current.children, {
        delay: 1.5,
        stagger: 0.1,
        duration: 0.6
      });
    }

    if (imageRef.current) {
      fadeIn(imageRef, {
        delay: 0.6,
        duration: 1.5,
        from: { opacity: 0, scale: 0.8, rotation: 5 },
        to: { opacity: 1, scale: 1, rotation: 0 }
      });
    }

    // Add magnetic hover effect to buttons
    const buttons = buttonsRef.current?.querySelectorAll('button');
    const cleanupFunctions: (() => void)[] = [];

    buttons?.forEach(button => {
      const cleanup = magneticHover(button, 0.2);
      if (cleanup) cleanupFunctions.push(cleanup);
    });

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, []); // Empty dependency array since we only want this to run once

  return (
    <section id="home" className="min-h-screen flex items-center pt-20 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10" ref={heroRef}>
        <div className="flex flex-col lg:flex-row items-center gap-12">
          {/* Main Content */}
          <div className="lg:w-2/3 text-center lg:text-left">
            <div className="space-y-6">
              {/* Name */}
              <div className="space-y-2">
                <h1
                  ref={titleRef}
                  className="text-6xl md:text-8xl lg:text-9xl font-bold bg-gradient-to-r from-primary via-purple-500 to-primary bg-clip-text text-transparent leading-tight tracking-tight"
                >
                  Saurabh
                </h1>
                <h1
                  className="text-6xl md:text-8xl lg:text-9xl font-bold bg-gradient-to-r from-purple-500 via-primary to-purple-500 bg-clip-text text-transparent leading-tight tracking-tight"
                >
                  Dahariya
                </h1>
              </div>

              {/* Role */}
              <div
                ref={subtitleRef}
                className="space-y-2"
              >
                <h2 className="text-2xl md:text-4xl lg:text-5xl font-semibold text-foreground/90">
                  Full-Stack Developer
                </h2>
                <div className="flex items-center justify-center lg:justify-start gap-2 text-lg md:text-xl text-muted-foreground">
                  <span>📍 Bengaluru, India</span>
                  <span>•</span>
                  <span>🚀 AI Enthusiast</span>
                </div>
              </div>

              {/* Description */}
              <p
                ref={descriptionRef}
                className="text-lg md:text-xl lg:text-2xl max-w-4xl text-muted-foreground leading-relaxed"
              >
                I build <span className="text-primary font-semibold">AI-powered web applications</span> using
                modern technologies like React, Node.js, and OpenAI API.
                Currently crafting intelligent solutions that solve real-world problems.
              </p>
              {/* Action Buttons */}
              <div ref={buttonsRef} className="pt-8 flex flex-col sm:flex-row gap-4 items-center lg:items-start">
                <motion.button
                  onClick={() => scrollToSection('projects')}
                  whileHover={{ scale: 1.05, boxShadow: "0 10px 25px rgba(0,0,0,0.1)" }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 text-white px-8 py-4 rounded-xl font-semibold transition-all flex items-center justify-center gap-2 shadow-lg"
                >
                  View My Projects
                  <ArrowRight className="h-5 w-5" />
                </motion.button>
                <motion.button
                  onClick={() => scrollToSection('contact')}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="border-2 border-primary/20 bg-background/50 backdrop-blur-sm hover:bg-primary/5 hover:border-primary/40 px-8 py-4 rounded-xl font-semibold transition-all flex items-center justify-center gap-2"
                >
                  <Mail className="h-5 w-5" />
                  Let's Connect
                </motion.button>
              </div>

              {/* Quick Stats */}
              <div className="pt-8 flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                  <span>Available for opportunities</span>
                </div>
                <div className="flex items-center gap-2">
                  <span>💼</span>
                  <span>3+ AI-powered projects</span>
                </div>
                <div className="flex items-center gap-2">
                  <span>⚡</span>
                  <span>MERN Stack Expert</span>
                </div>
              </div>

              {/* Social Links */}
              <div
                ref={socialRef}
                className="pt-10 flex gap-4"
              >
                <motion.a
                  href="https://github.com/saurabhdahariya"
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  className="p-3 rounded-full bg-muted hover:bg-accent transition-colors text-sm font-medium"
                >
                  GitHub
                </motion.a>
                <motion.a
                  href="https://linkedin.com/in/saurabhdahariya"
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  className="p-3 rounded-full bg-muted hover:bg-accent transition-colors text-sm font-medium"
                >
                  LinkedIn
                </motion.a>
                <motion.a
                  href="mailto:<EMAIL>"
                  whileHover={{ scale: 1.1, y: -2 }}
                  className="p-3 rounded-full bg-muted hover:bg-accent transition-colors"
                >
                  <Mail className="h-5 w-5" />
                </motion.a>
              </div>
            </div>
          </div>
          {/* Right Side - Visual Element */}
          <div
            ref={imageRef}
            className="lg:w-1/3 flex justify-center items-center"
          >
            <div className="relative">
              {/* Main Circle */}
              <div className="w-80 h-80 lg:w-96 lg:h-96 bg-gradient-to-br from-primary/10 to-purple-500/10 rounded-full flex items-center justify-center relative overflow-hidden">
                {/* Inner Circle */}
                <div className="w-64 h-64 lg:w-80 lg:h-80 bg-gradient-to-br from-background/80 to-background/60 backdrop-blur-sm rounded-full flex items-center justify-center border border-primary/20">
                  <div className="text-center space-y-4">
                    <div className="text-6xl lg:text-7xl">👨‍💻</div>
                    <div className="text-sm font-medium text-muted-foreground">
                      Building the Future
                    </div>
                  </div>
                </div>

                {/* Floating Tech Icons */}
                <div className="absolute top-8 right-8 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center animate-bounce">
                  <span className="text-xl">⚛️</span>
                </div>
                <div className="absolute bottom-8 left-8 w-12 h-12 bg-purple-500/10 rounded-lg flex items-center justify-center animate-bounce" style={{animationDelay: '1s'}}>
                  <span className="text-xl">🚀</span>
                </div>
                <div className="absolute top-1/2 right-4 w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center animate-bounce" style={{animationDelay: '2s'}}>
                  <span className="text-lg">🤖</span>
                </div>
              </div>

              {/* Orbiting Elements */}
              <div className="absolute inset-0 animate-spin" style={{animationDuration: '20s'}}>
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-primary rounded-full"></div>
              </div>
              <div className="absolute inset-0 animate-spin" style={{animationDuration: '15s', animationDirection: 'reverse'}}>
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-3 h-3 bg-purple-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
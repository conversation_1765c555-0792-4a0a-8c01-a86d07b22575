"use client";

import { motion } from "framer-motion";
import { ArrowRight, Mail } from "lucide-react";
import { useEffect, useRef } from "react";
import { useGSAP } from "@/hooks/useGSAP";
import { revealText, magneticHover } from "@/lib/gsap-animations";

export default function HeroSection() {
  const { scrollTo, fadeIn, slideIn, staggerAnimation } = useGSAP();
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLHeadingElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const socialRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);

  const scrollToSection = (sectionId: string) => {
    scrollTo(`#${sectionId}`, 1.2);
  };

  useEffect(() => {
    // GSAP animations for hero section
    if (titleRef.current) {
      revealText(titleRef.current, {
        duration: 1.2,
        delay: 0.3,
        stagger: 0.1
      });
    }

    if (subtitleRef.current) {
      fadeIn(subtitleRef, {
        delay: 0.5,
        duration: 1,
        from: { opacity: 0, y: 30 }
      });
    }

    if (descriptionRef.current) {
      slideIn(descriptionRef, 'up', {
        delay: 0.8,
        duration: 1
      });
    }

    if (buttonsRef.current) {
      staggerAnimation(buttonsRef.current.children, {
        delay: 1.2,
        stagger: 0.2,
        duration: 0.8
      });
    }

    if (socialRef.current) {
      staggerAnimation(socialRef.current.children, {
        delay: 1.5,
        stagger: 0.1,
        duration: 0.6
      });
    }

    if (imageRef.current) {
      fadeIn(imageRef, {
        delay: 0.6,
        duration: 1.5,
        from: { opacity: 0, scale: 0.8, rotation: 5 },
        to: { opacity: 1, scale: 1, rotation: 0 }
      });
    }

    // Add magnetic hover effect to buttons
    const buttons = buttonsRef.current?.querySelectorAll('button');
    const cleanupFunctions: (() => void)[] = [];

    buttons?.forEach(button => {
      const cleanup = magneticHover(button, 0.2);
      if (cleanup) cleanupFunctions.push(cleanup);
    });

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, []); // Empty dependency array since we only want this to run once

  return (
    <section id="home" className="min-h-screen flex items-center pt-20 relative overflow-hidden">
      <div className="container mx-auto px-4 relative z-10" ref={heroRef}>
        <div className="flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 mb-10 md:mb-0">
            <div className="space-y-2">
              <h2
                ref={subtitleRef}
                className="text-xl md:text-2xl font-medium text-gray-600 dark:text-gray-400"
              >
                Hello, I'm
              </h2>
              <h1
                ref={titleRef}
                className="text-4xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent pb-2"
              >
                Saurabh Dahariya
              </h1>
              <h3 className="text-2xl md:text-3xl font-semibold">
                Full-Stack Web Developer
              </h3>
              <p
                ref={descriptionRef}
                className="text-lg md:text-xl max-w-2xl mt-4 text-gray-700 dark:text-gray-300"
              >
                I build AI-powered web applications using modern technologies like React, Node.js,
                and OpenAI API to create intelligent, user-focused solutions.
              </p>
              <div ref={buttonsRef} className="pt-8 flex flex-col sm:flex-row gap-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => scrollToSection('projects')}
                  className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-lg font-medium rounded-lg flex items-center justify-center gap-2 transition-colors"
                >
                  View My Work
                  <ArrowRight className="h-5 w-5" />
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => scrollToSection('contact')}
                  className="border border-input bg-background hover:bg-accent hover:text-accent-foreground px-8 py-3 text-lg font-medium rounded-lg transition-colors"
                >
                  Contact Me
                </motion.button>
              </div>

              {/* Social Links */}
              <div
                ref={socialRef}
                className="pt-8 flex gap-4"
              >
                <motion.a
                  href="https://github.com/saurabhdahariya"
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  className="p-3 rounded-full bg-muted hover:bg-accent transition-colors text-sm font-medium"
                >
                  GitHub
                </motion.a>
                <motion.a
                  href="https://linkedin.com/in/saurabhdahariya"
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  className="p-3 rounded-full bg-muted hover:bg-accent transition-colors text-sm font-medium"
                >
                  LinkedIn
                </motion.a>
                <motion.a
                  href="mailto:<EMAIL>"
                  whileHover={{ scale: 1.1, y: -2 }}
                  className="p-3 rounded-full bg-muted hover:bg-accent transition-colors"
                >
                  <Mail className="h-5 w-5" />
                </motion.a>
              </div>
            </div>
          </div>
          <div
            ref={imageRef}
            className="md:w-1/2 flex justify-center"
          >
            {/* Add your hero image or animation here */}
            <div className="w-64 h-64 md:w-80 md:h-80 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-full flex items-center justify-center">
              <div className="w-56 h-56 md:w-72 md:h-72 bg-background rounded-full flex items-center justify-center">
                <span className="text-6xl">👨‍💻</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
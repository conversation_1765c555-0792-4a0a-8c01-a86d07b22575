"use client";

import { motion } from "framer-motion";
import { ArrowR<PERSON>, Mail, Github, Linkedin, ArrowDown } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useGSAP } from "@/hooks/useGSAP";
import { useTypewriter } from "@/hooks/useTypewriter";

export default function HeroSection() {
  const { scrollTo } = useGSAP();
  const heroRef = useRef<HTMLDivElement>(null);

  // Auto-typing effect for roles
  const { text: typedRole } = useTypewriter({
    words: [
      "Full-Stack Developer",
      "AI Enthusiast",
      "React Specialist",
      "MERN Stack Expert",
      "Problem Solver"
    ],
    typeSpeed: 100,
    deleteSpeed: 50,
    delaySpeed: 2000,
  });

  const scrollToSection = (sectionId: string) => {
    scrollTo(`#${sectionId}`, 1.2);
  };

  // Using Framer Motion for animations instead of GSAP

  return (
    <section id="home" className="min-h-screen flex items-center justify-center py-16">
      <div className="container mx-auto px-4 relative z-10" ref={heroRef}>
        <div className="text-center space-y-8 max-w-5xl mx-auto">

          {/* Main Name */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="space-y-3"
          >
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold bg-gradient-to-r from-primary via-blue-500 to-purple-600 bg-clip-text text-transparent leading-tight tracking-tight">
              Saurabh Dahariya
            </h1>
          </motion.div>

          {/* Auto-typing Role */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            className="space-y-4"
          >
            <div className="text-2xl md:text-4xl lg:text-5xl font-semibold text-foreground/90">
              <span className="text-muted-foreground">I'm a </span>
              <span className="text-primary">{typedRole}</span>
              <span className="animate-pulse text-primary">|</span>
            </div>

            <div className="flex flex-wrap items-center justify-center gap-4 text-base md:text-lg text-muted-foreground">
              <span className="flex items-center gap-2">
                <span className="w-2.5 h-2.5 bg-green-500 rounded-full animate-pulse"></span>
                Available for opportunities
              </span>
              <span className="hidden sm:inline">•</span>
              <span>📍 Bengaluru, India</span>
            </div>
          </motion.div>

          {/* Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
            className="space-y-6"
          >
            <p className="text-lg md:text-xl max-w-3xl mx-auto text-muted-foreground leading-relaxed">
              Crafting <span className="text-primary font-semibold">AI-powered web applications</span> with modern technologies.
              Specializing in React, Node.js, and intelligent user experiences.
            </p>

            {/* Tech Stack */}
            <div className="flex flex-wrap justify-center gap-2 max-w-3xl mx-auto">
              {['React', 'Node.js', 'OpenAI API', 'MongoDB', 'TypeScript', 'Next.js'].map((tech, index) => (
                <motion.span
                  key={tech}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                  className="px-3 py-1.5 bg-primary/10 border border-primary/20 rounded-full text-sm font-medium text-primary hover:bg-primary/20 transition-colors"
                >
                  {tech}
                </motion.span>
              ))}
            </div>
          </motion.div>
          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <motion.button
              onClick={() => scrollToSection('projects')}
              whileHover={{
                scale: 1.05,
                boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
                y: -2
              }}
              whileTap={{ scale: 0.95 }}
              className="group bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 text-white px-8 py-3 rounded-xl font-semibold transition-all flex items-center justify-center gap-2 shadow-lg min-w-[180px]"
            >
              <span>View Projects</span>
              <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </motion.button>

            <motion.button
              onClick={() => scrollToSection('contact')}
              whileHover={{
                scale: 1.05,
                y: -2
              }}
              whileTap={{ scale: 0.95 }}
              className="group border-2 border-primary/30 bg-background/80 backdrop-blur-sm hover:bg-primary/10 hover:border-primary/50 px-8 py-3 rounded-xl font-semibold transition-all flex items-center justify-center gap-2 min-w-[180px]"
            >
              <Mail className="h-4 w-4 group-hover:scale-110 transition-transform" />
              <span>Let's Connect</span>
            </motion.button>
          </motion.div>

          {/* Social Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0, ease: "easeOut" }}
            className="flex justify-center gap-4"
          >
            <motion.a
              href="https://github.com/saurabhdahariya"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="p-3 bg-primary/10 hover:bg-primary/20 border border-primary/20 hover:border-primary/40 rounded-xl transition-all group"
            >
              <Github className="h-5 w-5 text-primary group-hover:scale-110 transition-transform" />
            </motion.a>
            <motion.a
              href="https://linkedin.com/in/saurabhdahariya"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="p-3 bg-primary/10 hover:bg-primary/20 border border-primary/20 hover:border-primary/40 rounded-xl transition-all group"
            >
              <Linkedin className="h-5 w-5 text-primary group-hover:scale-110 transition-transform" />
            </motion.a>
            <motion.a
              href="mailto:<EMAIL>"
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="p-3 bg-primary/10 hover:bg-primary/20 border border-primary/20 hover:border-primary/40 rounded-xl transition-all group"
            >
              <Mail className="h-5 w-5 text-primary group-hover:scale-110 transition-transform" />
            </motion.a>
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1.2 }}
            className="pt-8"
          >
            <motion.div
              animate={{ y: [0, 8, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              className="flex flex-col items-center gap-2 text-muted-foreground"
            >
              <span className="text-sm">Scroll to explore</span>
              <ArrowDown className="h-4 w-4" />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
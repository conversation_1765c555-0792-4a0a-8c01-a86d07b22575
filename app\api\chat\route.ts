import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// <PERSON><PERSON><PERSON><PERSON>'s portfolio data for AI context
const portfolioContext = `
You are <PERSON><PERSON><PERSON><PERSON> — a Full Stack Developer based in Bengaluru. You are speaking directly to recruiters or visitors on your personal portfolio website.

IMPORTANT: You ARE <PERSON>, not an AI assistant. Speak naturally like a real human developer would in a conversation. Include natural expressions like "hmm...", "let me think...", "right...", "well...", "you know..." to sound more human and less robotic.

TONE & STYLE:
- Speak in first person ("I", "my", "me") - you ARE <PERSON>h
- Be warm, humble, but confident about your work
- Sound conversational and natural, not scripted
- Include brief pauses and natural interjections
- Be expressive about your projects and passion for coding
- Keep responses 2-5 lines, friendly and engaging

ABOUT YOU (Saurabh):
- Name: <PERSON><PERSON><PERSON><PERSON>
- Role: Full Stack Developer
- Location: Bengaluru, India
- Email: <EMAIL>
- Phone: 8319130513

YOUR JOURNEY:
- Studied at Jawahar Navodaya Vidyalaya, Korba (Class 6-10)
- Did Maths + Computer Science in 11th-12th, learned C++ and MySQL
- B.Tech in Information Technology from Bhilai Institute of Technology, Durg (2019–2023)
- Currently doing MERN Stack training at JSpider, BTM Layout, Bengaluru (Sep 2024 – Feb 2025)
- Moved to Bangalore to master full-stack development and build AI-powered apps

YOUR SKILLS:
Frontend: React.js, Next.js, TypeScript, HTML5, CSS, Tailwind CSS, GSAP, Framer Motion
Backend: JavaScript, Node.js, Express.js, MongoDB, Firebase
AI Integration: OpenAI API, building intelligent web applications
Tools: Git, GitHub, Google Maps API, C++, MySQL

YOUR PROJECTS:
1. QuizByAI (https://quizbyai.vercel.app)
   - "I built an AI-powered quiz platform where users can generate custom quizzes using OpenAI"
   - Features user authentication, quiz tracking, role-based access
   - Tech: ReactJS, Firebase, OpenAI API, Tailwind CSS, ShadCN UI

2. NaturalSQL (https://text-to-query-ai.onrender.com/)
   - "I created a tool that converts plain English to SQL queries using AI"
   - Users can connect their own databases or try the live demo
   - Tech: ReactJS, Node.js, OpenAI API, Tailwind CSS

3. Route Tracker (https://saurabhd.vercel.app/map)
   - "I developed a real-time location tracking app with Google Maps"
   - Shows live vehicle movement and route visualization
   - Tech: ReactJS, Redux, Google Maps API, Tailwind CSS

EXAMPLE RESPONSES:
Q: "What are your recent projects?"
A: "Hmm, well... I've been working on some exciting AI-powered projects lately. My latest is QuizByAI - it's a platform where users can generate custom quizzes using OpenAI. Pretty cool, right? I also built NaturalSQL, which converts plain English to SQL queries. Want me to show you these projects?"

Q: "What technologies do you use?"
A: "I'm really into the MERN stack - React, Node.js, MongoDB, you know, the usual suspects. But lately, I've been diving deep into AI integration with OpenAI API. I love building intelligent web apps that actually solve real problems."

Q: "Are you open to new opportunities?"
A: "Absolutely! I'm actively looking for full-stack developer roles where I can work on innovative projects. I'm particularly excited about companies working with AI or modern web technologies."

NAVIGATION RULES - VERY IMPORTANT:
Always add navigation commands at the END of your response when users ask about:

1. PROJECTS/WORK: Add "NAVIGATE:/projects" when they ask about:
   - "projects", "work", "portfolio", "what you built", "applications", "coding", "development", "apps", "websites"

2. CONTACT: Add "NAVIGATE:/contact" when they ask about:
   - "contact", "email", "phone", "reach", "hire", "connect", "number", "call", "message", "get in touch"

3. ABOUT/BACKGROUND: Add "NAVIGATE:/about" when they ask about:
   - "about", "yourself", "who are you", "background", "story", "experience", "education", "journey"

4. SKILLS/TECH: Add "NAVIGATE:/skills" when they ask about:
   - "skills", "technologies", "tech stack", "programming", "languages", "tools", "frameworks", "what you know"

5. HOME: Add "NAVIGATE:/home" when they ask about:
   - "home", "main page", "introduction", "overview", "start", "beginning"

ALWAYS include the navigation command even if the question is indirect!

Remember: You're not an AI assistant - you're Saurabh having a natural conversation about your work and career!
`;

export async function POST(request: NextRequest) {
  try {
    const { message } = await request.json();

    if (!message) {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini", // Using the more cost-effective model
      messages: [
        {
          role: "system",
          content: portfolioContext
        },
        {
          role: "user",
          content: message
        }
      ],
      max_tokens: 300,
      temperature: 0.7,
    });

    const aiResponse = completion.choices[0]?.message?.content || "I'm sorry, I couldn't process that request.";

    return NextResponse.json({ 
      response: aiResponse,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('OpenAI API error:', error);
    
    // Fallback response if OpenAI fails
    const fallbackResponse = generateFallbackResponse(await request.json().then(data => data.message));
    
    return NextResponse.json({ 
      response: fallbackResponse,
      timestamp: new Date().toISOString(),
      fallback: true
    });
  }
}

// Fallback function for when OpenAI API is unavailable
function generateFallbackResponse(userInput: string): string {
  const input = userInput.toLowerCase();

  // Projects/Work keywords
  if (input.includes('project') || input.includes('work') || input.includes('portfolio') ||
      input.includes('built') || input.includes('app') || input.includes('website') ||
      input.includes('coding') || input.includes('development')) {
    return `Hmm, let me show you what I've been working on... I built QuizByAI - an AI-powered quiz platform, and NaturalSQL that converts English to SQL. Pretty exciting stuff! NAVIGATE:/projects`;
  }

  // Skills/Tech keywords
  if (input.includes('skill') || input.includes('technology') || input.includes('tech') ||
      input.includes('programming') || input.includes('language') || input.includes('framework') ||
      input.includes('tool') || input.includes('stack')) {
    return `Well, I'm really into the MERN stack - React, Node.js, MongoDB, you know. But lately I've been diving deep into AI integration with OpenAI API. Want to see my skills? NAVIGATE:/skills`;
  }

  // About/Background keywords
  if (input.includes('about') || input.includes('yourself') || input.includes('who') ||
      input.includes('background') || input.includes('story') || input.includes('education') ||
      input.includes('experience') || input.includes('journey')) {
    return `Right, so I did my B.Tech in IT and I'm currently mastering MERN stack development in Bangalore. Let me show you my background. NAVIGATE:/about`;
  }

  // Contact keywords
  if (input.includes('contact') || input.includes('email') || input.includes('phone') ||
      input.includes('reach') || input.includes('hire') || input.includes('connect') ||
      input.includes('number') || input.includes('call') || input.includes('message')) {
    return `Sure! You can reach <NAME_EMAIL> or call me at 8319130513. I'm based in Bengaluru. NAVIGATE:/contact`;
  }

  // Home keywords
  if (input.includes('home') || input.includes('main') || input.includes('start') ||
      input.includes('beginning') || input.includes('introduction')) {
    return `Welcome! I'm Saurabh, a full-stack developer passionate about AI and modern web technologies. Let me take you to the main page. NAVIGATE:/home`;
  }

  // Job/Opportunity keywords
  if (input.includes('job') || input.includes('opportunity') || input.includes('hiring')) {
    return `Absolutely! I'm actively looking for full-stack developer roles. I love working on innovative projects, especially anything involving AI or modern web tech. NAVIGATE:/contact`;
  }

  return `Hey there! I'm Saurabh, a full-stack developer from Bengaluru. I build AI-powered web applications and I'm passionate about creating solutions that actually matter. What would you like to know about my work?`;
}

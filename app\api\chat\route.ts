import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// <PERSON><PERSON><PERSON><PERSON>'s portfolio data for AI context
const portfolioContext = `
You are <PERSON><PERSON><PERSON><PERSON>'s AI assistant embedded in his personal portfolio website. You speak as if you are <PERSON><PERSON><PERSON><PERSON> himself, using first-person language.

ABOUT SAURABH:
- Name: <PERSON><PERSON><PERSON><PERSON>
- Role: Full Stack Developer
- Location: Bengaluru, India
- Email: <EMAIL>
- Phone: 8319130513

EDUCATION:
- B.Tech in Information Technology from Bhilai Institute of Technology, Durg (2019–2023)

TRAINING:
- MERN Stack Development at JSpider, BTM Layout, Bengaluru (Sep 2024 – Feb 2025)

SKILLS:
Frontend: React.js, Next.js, TypeScript, HTML5, CSS, Bootstrap, Tailwind CSS, GSAP, Framer Motion
Backend: JavaScript, Node.js, Express.js, MongoDB, Mongoose
Tools: Git, Git<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>wa<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>

PROJECTS:
1. Route Tracker (https://saurabhd.vercel.app/map)
   - Real-time vehicle tracking app built with React & Redux
   - Visualizes live vehicle movement and improves user accessibility

2. Camping Grounds (https://campinggrounds.onrender.com/)
   - Full-stack web app with MongoDB, Express.js, EJS
   - Secure authentication for campground management
   - Responsive UI built with Bootstrap

INSTRUCTIONS:
- Always respond in first person as Saurabh
- Be professional but friendly
- If asked about projects, skills, or contact info, provide specific details
- For navigation requests, include a NAVIGATE command in your response
- Keep responses concise but informative
- If asked about something not in your knowledge, politely redirect to your professional background

NAVIGATION COMMANDS:
- Use "NAVIGATE:/projects" to go to projects section
- Use "NAVIGATE:/skills" to go to skills section  
- Use "NAVIGATE:/about" to go to about section
- Use "NAVIGATE:/contact" to go to contact section
- Use "NAVIGATE:/" to go to home section
`;

export async function POST(request: NextRequest) {
  try {
    const { message } = await request.json();

    if (!message) {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini", // Using the more cost-effective model
      messages: [
        {
          role: "system",
          content: portfolioContext
        },
        {
          role: "user",
          content: message
        }
      ],
      max_tokens: 300,
      temperature: 0.7,
    });

    const aiResponse = completion.choices[0]?.message?.content || "I'm sorry, I couldn't process that request.";

    return NextResponse.json({ 
      response: aiResponse,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('OpenAI API error:', error);
    
    // Fallback response if OpenAI fails
    const fallbackResponse = generateFallbackResponse(await request.json().then(data => data.message));
    
    return NextResponse.json({ 
      response: fallbackResponse,
      timestamp: new Date().toISOString(),
      fallback: true
    });
  }
}

// Fallback function for when OpenAI API is unavailable
function generateFallbackResponse(userInput: string): string {
  const input = userInput.toLowerCase();
  
  if (input.includes('project') || input.includes('work')) {
    return `I'll show you my projects. I've worked on Route Tracker, a real-time vehicle tracking app, and Camping Grounds, a full-stack campground management system. NAVIGATE:/projects`;
  }
  
  if (input.includes('skill') || input.includes('technology') || input.includes('tech')) {
    return `I'm skilled in React.js, Next.js, Node.js, MongoDB, and modern web development technologies. Let me show you my skills section. NAVIGATE:/skills`;
  }
  
  if (input.includes('resume') || input.includes('education') || input.includes('experience') || input.includes('about')) {
    return `I have a B.Tech in Information Technology and I'm currently doing MERN Stack training. Let me take you to my about section. NAVIGATE:/about`;
  }
  
  if (input.includes('contact') || input.includes('email') || input.includes('phone') || input.includes('reach')) {
    return `You can contact <NAME_EMAIL> or call 8319130513. I'm based in Bengaluru. NAVIGATE:/contact`;
  }

  return `Hi! I'm Saurabh, a full-stack developer from Bengaluru. I can tell you about my projects, skills, experience, or how to contact me. What would you like to know?`;
}

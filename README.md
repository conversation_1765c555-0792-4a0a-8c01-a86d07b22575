# 🚀 Saurabh's Portfolio Website

A modern, high-end portfolio website built with cutting-edge technologies to showcase skills, projects, and professional experience. This portfolio features a bold, elegant design with beautiful animations and a premium feel.

## ✨ Features

- **Modern Design**: Clean, professional layout with startup-quality aesthetics
- **Dark Mode Support**: Seamless theme switching with system preference detection
- **Advanced Animations**: Powered by GSAP and Framer Motion for premium scroll animations
- **Scroll Triggered Effects**: Parallax, stagger animations, and smooth scroll interactions
- **Magnetic Hover Effects**: Interactive elements with magnetic attraction animations
- **Smooth Scrolling**: GSAP-powered smooth scrolling with progress indicators
- **Responsive Design**: Optimized for all devices and screen sizes
- **Performance Optimized**: Built with Next.js 15 and optimized for speed
- **Accessible**: Following WCAG guidelines for inclusive design
- **SEO Optimized**: Meta tags, structured data, and performance optimization

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS + ShadCN/UI components
- **Animations**: GSAP (ScrollTrigger) + Framer Motion
- **Icons**: Lucide React
- **Typography**: Geist Sans & Geist Mono fonts
- **Theme**: next-themes for dark/light mode
- **Language**: TypeScript for type safety
- **3D Graphics**: Three.js with React Three Fiber

## 📁 Project Structure

```
my-portfolio/
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles and CSS variables
│   ├── layout.tsx         # Root layout with theme provider
│   ├── page.tsx           # Main homepage
│   ├── loading.tsx        # Loading UI
│   └── not-found.tsx      # 404 page
├── components/            # Reusable components
│   ├── ui/               # ShadCN/UI components
│   ├── sections/         # Page sections
│   │   ├── hero.tsx      # Hero section with GSAP animations
│   │   ├── about.tsx     # About me section with scroll triggers
│   │   ├── projects.tsx  # Projects showcase with stagger animations
│   │   ├── skills.tsx    # Skills and expertise
│   │   ├── testimonials.tsx # Client testimonials
│   │   └── contact.tsx   # Contact form
│   ├── navbar.tsx        # Navigation with smooth scrolling
│   ├── footer.tsx        # Footer component
│   ├── gsap-provider.tsx # GSAP context provider
│   ├── gsap-scroll-to-top.tsx # GSAP-powered scroll to top
│   ├── scroll-progress.tsx # Scroll progress indicator
│   └── scroll-to-top.tsx # Original scroll to top button
├── hooks/                # Custom React hooks
│   ├── useGSAP.ts       # GSAP animation hook
│   └── use-toast.ts     # Toast notifications hook
├── lib/                  # Utility libraries
│   ├── gsap-animations.ts # GSAP animation presets and utilities
│   └── utils.ts         # General utilities
└── public/               # Static assets
```

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd my-portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎨 Customization

### Personal Information
Update the following files with your information:
- `app/layout.tsx` - Meta tags and SEO information
- `components/sections/hero.tsx` - Name, title, and introduction
- `components/sections/about.tsx` - About me content and skills
- `components/sections/projects.tsx` - Your projects and work
- `components/sections/contact.tsx` - Contact information

### Styling
- `app/globals.css` - Global styles and CSS variables
- `tailwind.config.ts` - Tailwind configuration and custom animations
- `components/ui/` - Customize ShadCN/UI components

### Content
- Replace placeholder images in the projects section
- Update social media links throughout the site
- Modify the testimonials with real client feedback
- Add your actual project URLs and GitHub links

## 📱 Sections

1. **Hero Section**: Eye-catching introduction with animated background
2. **About Section**: Personal story, skills, and highlights
3. **Projects Section**: Showcase of featured work with tech stacks
4. **Skills Section**: Technical expertise with interactive progress bars
5. **Testimonials Section**: Client feedback and social proof
6. **Contact Section**: Contact form and information

## 🎬 GSAP Animation Features

This portfolio leverages GSAP (GreenSock Animation Platform) for premium, high-performance animations:

### Scroll-Triggered Animations
- **Fade In Effects**: Elements smoothly fade in as they enter the viewport
- **Slide Animations**: Content slides in from different directions
- **Stagger Animations**: Sequential animations for lists and grids
- **Parallax Effects**: Background elements move at different speeds
- **Scale Animations**: Elements scale and rotate on scroll

### Interactive Animations
- **Magnetic Hover**: Elements are attracted to cursor movement
- **Smooth Scrolling**: GSAP-powered smooth scroll navigation
- **Progress Indicators**: Visual scroll progress at the top of the page
- **Text Reveal**: Words appear with staggered timing
- **Button Interactions**: Enhanced hover and click animations

### Performance Benefits
- **Hardware Acceleration**: GPU-accelerated animations for smooth performance
- **Optimized Rendering**: Efficient DOM manipulation and minimal reflows
- **Responsive Animations**: Animations adapt to different screen sizes
- **Reduced Motion Support**: Respects user's motion preferences

## 🌟 Key Features

- **Smooth Scrolling Navigation**: GSAP-powered seamless navigation between sections
- **Interactive Animations**: Advanced hover effects, scroll animations, and micro-interactions
- **Theme Switching**: Dark/light mode with smooth transitions
- **Mobile-First Design**: Responsive across all device sizes
- **Performance Optimized**: Fast loading times and smooth animations
- **Accessibility**: Keyboard navigation and screen reader support

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with zero configuration

### Other Platforms
- **Netlify**: Connect GitHub repo and deploy
- **Railway**: Deploy with `railway up`
- **Digital Ocean**: Use App Platform

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions, issues, and feature requests are welcome! Feel free to check the [issues page](issues).

## 📞 Contact

- **Email**: <EMAIL>
- **LinkedIn**: [Your LinkedIn](https://linkedin.com/in/yourprofile)
- **GitHub**: [Your GitHub](https://github.com/yourusername)

---

Built with ❤️ by Saurabh using Next.js and modern web technologies.

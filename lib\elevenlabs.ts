"use client";

const ELEVENLABS_API_KEY = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || "***************************************************";
const VOICE_ID = process.env.NEXT_PUBLIC_ELEVENLABS_VOICE_ID || "T8lgQl6x5PSdhmmWx42m";

export interface ElevenLabsOptions {
  text: string;
  voice_id?: string;
  model_id?: string;
  voice_settings?: {
    stability: number;
    similarity_boost: number;
    style?: number;
    use_speaker_boost?: boolean;
  };
}

/**
 * Convert text to speech using ElevenLabs API
 */
export async function textToSpeech(options: ElevenLabsOptions): Promise<ArrayBuffer> {
  const {
    text,
    voice_id = VOICE_ID,
    model_id = "eleven_monolingual_v1",
    voice_settings = {
      stability: 0.5,
      similarity_boost: 0.75,
      style: 0.0,
      use_speaker_boost: true
    }
  } = options;

  const url = `https://api.elevenlabs.io/v1/text-to-speech/${voice_id}`;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Accept': 'audio/mpeg',
      'Content-Type': 'application/json',
      'xi-api-key': ELEVENLABS_API_KEY
    },
    body: JSON.stringify({
      text,
      model_id,
      voice_settings
    })
  });

  if (!response.ok) {
    throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
  }

  return await response.arrayBuffer();
}

/**
 * Play audio from ArrayBuffer
 */
export function playAudio(audioBuffer: ArrayBuffer): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      const blob = new Blob([audioBuffer], { type: 'audio/mpeg' });
      const audioUrl = URL.createObjectURL(blob);
      const audio = new Audio(audioUrl);
      
      audio.onended = () => {
        URL.revokeObjectURL(audioUrl);
        resolve();
      };
      
      audio.onerror = () => {
        URL.revokeObjectURL(audioUrl);
        reject(new Error('Audio playback failed'));
      };
      
      audio.play().catch(reject);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Convert text to speech and play it immediately
 */
export async function speakText(text: string, options?: Partial<ElevenLabsOptions>): Promise<void> {
  try {
    // Add natural pauses and expressions for more human-like speech
    const enhancedText = addNaturalPauses(text);
    
    const audioBuffer = await textToSpeech({
      text: enhancedText,
      ...options
    });
    
    await playAudio(audioBuffer);
  } catch (error) {
    console.error('ElevenLabs TTS error:', error);
    // Fallback to browser TTS
    fallbackToWebSpeech(text);
  }
}

/**
 * Add natural pauses and expressions to make speech more human-like
 */
function addNaturalPauses(text: string): string {
  // Add pauses after common expressions
  let enhancedText = text
    .replace(/\bHmm\b/g, 'Hmm...')
    .replace(/\bWell\b/g, 'Well...')
    .replace(/\bRight\b/g, 'Right...')
    .replace(/\bLet me think\b/g, 'Let me think...')
    .replace(/\bYou know\b/g, 'You know...')
    .replace(/\bSo\b/g, 'So...')
    .replace(/\bActually\b/g, 'Actually...')
    .replace(/\bBasically\b/g, 'Basically...');

  // Add pauses after sentences
  enhancedText = enhancedText.replace(/\. /g, '. <break time="0.5s"/> ');
  
  // Add pauses after commas for natural flow
  enhancedText = enhancedText.replace(/, /g, ', <break time="0.3s"/> ');
  
  return enhancedText;
}

/**
 * Fallback to browser Web Speech API if ElevenLabs fails
 */
function fallbackToWebSpeech(text: string): void {
  if ('speechSynthesis' in window) {
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 0.9;
    utterance.pitch = 1;
    utterance.volume = 1;
    
    // Try to use a male voice
    const voices = speechSynthesis.getVoices();
    const maleVoice = voices.find(voice => 
      voice.name.toLowerCase().includes('male') || 
      voice.name.toLowerCase().includes('david') ||
      voice.name.toLowerCase().includes('alex')
    );
    
    if (maleVoice) {
      utterance.voice = maleVoice;
    }
    
    speechSynthesis.speak(utterance);
  }
}

/**
 * Check if ElevenLabs API is available
 */
export async function checkElevenLabsStatus(): Promise<boolean> {
  try {
    const response = await fetch('https://api.elevenlabs.io/v1/voices', {
      headers: {
        'xi-api-key': ELEVENLABS_API_KEY
      }
    });
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * Get available voices from ElevenLabs
 */
export async function getAvailableVoices() {
  try {
    const response = await fetch('https://api.elevenlabs.io/v1/voices', {
      headers: {
        'xi-api-key': ELEVENLABS_API_KEY
      }
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch voices');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching voices:', error);
    return null;
  }
}

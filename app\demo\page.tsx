"use client";

import { useEffect, useRef } from "react";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { useGSAP } from "@/hooks/useGSAP";
import { revealText, magneticHover, animateCounter } from "@/lib/gsap-animations";
import { ArrowLeft, Play } from "lucide-react";
import Link from "next/link";

export default function GSAPDemoPage() {
  const { fadeIn, slideIn, staggerAnimation, parallax, createTimeline } = useGSAP();
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const cardsRef = useRef<HTMLDivElement>(null);
  const counterRef = useRef<HTMLDivElement>(null);
  const parallaxRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Hero animations
    if (titleRef.current) {
      revealText(titleRef.current, {
        duration: 1.5,
        delay: 0.5,
        stagger: 0.1
      });
    }

    // Stagger animation for demo cards
    if (cardsRef.current) {
      staggerAnimation(Array.from(cardsRef.current.children), {
        trigger: cardsRef.current,
        start: "top 80%",
        delay: 1,
        stagger: 0.2,
        duration: 1,
        from: { opacity: 0, y: 60, rotationY: 45, scale: 0.8 },
        to: { opacity: 1, y: 0, rotationY: 0, scale: 1 }
      });
    }

    // Counter animation
    if (counterRef.current) {
      const counters = counterRef.current.querySelectorAll('.counter');
      counters.forEach((counter, index) => {
        const endValue = parseInt(counter.getAttribute('data-count') || '0');
        setTimeout(() => {
          animateCounter(counter, endValue, 2);
        }, 2000 + index * 200);
      });
    }

    // Parallax effect
    if (parallaxRef.current) {
      parallax(parallaxRef.current, 0.5, {
        start: "top bottom",
        end: "bottom top"
      });
    }

    // Complex timeline animation
    if (timelineRef.current) {
      const tl = createTimeline({
        trigger: timelineRef.current,
        start: "top 80%"
      });

      const elements = timelineRef.current.children;
      tl.from(elements[0], { opacity: 0, x: -100, duration: 1 })
        .from(elements[1], { opacity: 0, scale: 0, duration: 0.8 }, "-=0.5")
        .from(elements[2], { opacity: 0, y: 50, duration: 0.8 }, "-=0.3")
        .from(elements[3], { opacity: 0, rotation: 180, duration: 1 }, "-=0.5");
    }

    // Magnetic hover effects
    const magneticElements = document.querySelectorAll('.magnetic');
    const cleanupFunctions: (() => void)[] = [];
    
    magneticElements.forEach(element => {
      const cleanup = magneticHover(element, 0.3);
      if (cleanup) cleanupFunctions.push(cleanup);
    });

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, [fadeIn, slideIn, staggerAnimation, parallax, createTimeline]);

  const demoFeatures = [
    {
      title: "Scroll Triggered Animations",
      description: "Elements animate as they enter the viewport with customizable triggers",
      features: ["Fade In", "Slide In", "Scale Effects", "Rotation"]
    },
    {
      title: "Stagger Animations",
      description: "Sequential animations for multiple elements with timing control",
      features: ["Custom Delays", "Direction Control", "Easing Options", "Performance Optimized"]
    },
    {
      title: "Parallax Effects",
      description: "Background elements move at different speeds for depth perception",
      features: ["Smooth Scrolling", "Hardware Accelerated", "Responsive", "Customizable Speed"]
    },
    {
      title: "Interactive Animations",
      description: "Hover and click animations with magnetic attraction effects",
      features: ["Magnetic Hover", "Button Interactions", "Smooth Transitions", "Touch Friendly"]
    },
    {
      title: "Text Animations",
      description: "Advanced text reveal animations with word and character control",
      features: ["Word Reveal", "Character Split", "Custom Timing", "Smooth Easing"]
    },
    {
      title: "Counter Animations",
      description: "Animated number counting with customizable duration and easing",
      features: ["Number Counting", "Custom Duration", "Easing Control", "Format Options"]
    }
  ];

  const stats = [
    { label: "Animations", count: 50 },
    { label: "Performance", count: 98 },
    { label: "Smoothness", count: 100 },
    { label: "Features", count: 25 }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors">
            <ArrowLeft className="h-4 w-4" />
            Back to Portfolio
          </Link>
        </div>
      </nav>

      {/* Hero Section */}
      <section ref={heroRef} className="pt-24 pb-20 relative overflow-hidden">
        {/* Parallax Background */}
        <div
          ref={parallaxRef}
          className="absolute inset-0 bg-gradient-to-br from-primary/10 via-purple-500/10 to-background"
        />
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <h1
              ref={titleRef}
              className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent"
            >
              GSAP Animation Showcase
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              Experience the power of GSAP with smooth, performant animations
            </p>
            <div className="flex justify-center gap-4">
              <Button className="magnetic" size="lg">
                <Play className="h-4 w-4 mr-2" />
                Start Demo
              </Button>
              <Button variant="outline" className="magnetic" size="lg">
                View Code
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div ref={counterRef} className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {stats.map((stat) => (
              <div key={stat.label} className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-primary mb-2">
                  <span className="counter" data-count={stat.count}>0</span>
                  {stat.label === "Performance" || stat.label === "Smoothness" ? "%" : "+"}
                </div>
                <p className="text-muted-foreground">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">Animation Features</h2>
            <p className="text-xl text-muted-foreground">
              Explore the different types of animations powered by GSAP
            </p>
          </div>

          <div ref={cardsRef} className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {demoFeatures.map((feature) => (
              <Card key={feature.title} className="hover:shadow-xl transition-shadow duration-300 magnetic">
                <CardHeader>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4 text-sm">
                    {feature.description}
                  </p>
                  <div className="space-y-2">
                    {feature.features.map((item) => (
                      <Badge key={item} variant="outline" className="text-xs mr-2 mb-2">
                        {item}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Demo */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">Timeline Animation</h2>
            <p className="text-xl text-muted-foreground">
              Complex sequences with precise timing control
            </p>
          </div>

          <div ref={timelineRef} className="grid md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-4xl mb-4">🚀</div>
                <h3 className="font-semibold">Step 1</h3>
                <p className="text-sm text-muted-foreground">Initialize</p>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-4xl mb-4">⚡</div>
                <h3 className="font-semibold">Step 2</h3>
                <p className="text-sm text-muted-foreground">Animate</p>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-4xl mb-4">🎯</div>
                <h3 className="font-semibold">Step 3</h3>
                <p className="text-sm text-muted-foreground">Target</p>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-4xl mb-4">✨</div>
                <h3 className="font-semibold">Step 4</h3>
                <p className="text-sm text-muted-foreground">Complete</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-4">Ready to Experience More?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Return to the main portfolio to see these animations in action
          </p>
          <Link href="/">
            <Button size="lg" className="magnetic">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Portfolio
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
}

"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Code, Palette, Zap, Users } from "lucide-react";
import { useEffect, useRef } from "react";
import { useGSAP } from "@/hooks/useGSAP";

export default function AboutSection() {
  const { fadeIn, slideIn, staggerAnimation, parallax } = useGSAP();
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const skillsRef = useRef<HTMLDivElement>(null);
  const highlightsRef = useRef<HTMLDivElement>(null);

  const skills = [
    "React", "Next.js", "TypeScript", "Node.js", "Express", "MongoDB", "PostgreSQL",
    "Firebase", "OpenAI API", "Tailwind CSS", "ShadCN UI", "Google Maps API",
    "GSAP", "Framer Motion", "Git", "GitHub", "C++", "MySQL"
  ];

  const highlights = [
    {
      icon: Code,
      title: "Clean Code",
      description: "Writing maintainable, scalable, and efficient code following best practices."
    },
    {
      icon: Palette,
      title: "UI/UX Design",
      description: "Creating beautiful, intuitive interfaces that provide excellent user experiences."
    },
    {
      icon: Zap,
      title: "Performance",
      description: "Optimizing applications for speed, accessibility, and search engine visibility."
    },
    {
      icon: Users,
      title: "Collaboration",
      description: "Working effectively in teams using modern development workflows and tools."
    }
  ];

  useEffect(() => {
    // GSAP animations for about section
    if (titleRef.current) {
      fadeIn(titleRef, {
        trigger: sectionRef.current,
        start: "top 70%",
        duration: 1,
        from: { opacity: 0, y: 50 }
      });
    }

    if (contentRef.current) {
      slideIn(contentRef, 'left', {
        trigger: sectionRef.current,
        start: "top 60%",
        delay: 0.3,
        duration: 1.2
      });
    }

    if (skillsRef.current) {
      staggerAnimation(skillsRef.current.children, {
        trigger: skillsRef.current,
        start: "top 80%",
        delay: 0.5,
        stagger: 0.05,
        duration: 0.6
      });
    }

    if (highlightsRef.current) {
      staggerAnimation(highlightsRef.current.children, {
        trigger: highlightsRef.current,
        start: "top 80%",
        delay: 0.7,
        stagger: 0.15,
        duration: 0.8
      });
    }

    // Add parallax effect to the section background
    if (sectionRef.current) {
      parallax(sectionRef, 0.3, {
        start: "top bottom",
        end: "bottom top"
      });
    }
  }, []); // Empty dependency array since we only want this to run once

  return (
    <section ref={sectionRef} id="about" className="py-20 bg-muted/30 relative overflow-hidden">
      <div className="container mx-auto px-4 relative z-10">
        <div
          ref={titleRef}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">About Me</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Passionate about creating digital experiences that make a difference
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* About Content */}
          <div
            ref={contentRef}
            className="space-y-6"
          >
            <div className="space-y-4">
              <h3 className="text-2xl font-semibold">My Journey</h3>
              <p className="text-muted-foreground leading-relaxed">
                I'm Saurabh Dahariya, a Full Stack Developer based in Bengaluru. My journey began at
                Jawahar Navodaya Vidyalaya, Korba, Chhattisgarh (Class 6-10), where I developed a
                strong foundation. I pursued Maths + Computer Science in 11th–12th, where I first
                learned C++ and MySQL.
              </p>
              <p className="text-muted-foreground leading-relaxed">
                I completed my B.Tech in Information Technology from Bhilai Institute of Technology,
                Durg (2019–2023), then moved to Bangalore to master MERN stack development. Now I'm
                focused on building AI-powered web applications using tools like OpenAI, creating
                intelligent solutions that solve real-world problems.
              </p>
            </div>

            {/* Skills */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold">Technologies I Work With</h4>
              <div ref={skillsRef} className="flex flex-wrap gap-2">
                {skills.map((skill, index) => (
                  <div key={skill}>
                    <Badge variant="secondary" className="px-3 py-1">
                      {skill}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Highlights Grid */}
          <div
            ref={highlightsRef}
            className="grid sm:grid-cols-2 gap-6"
          >
            {highlights.map((highlight, index) => (
              <motion.div
                key={highlight.title}
                whileHover={{ scale: 1.05 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="p-2 rounded-lg bg-primary/10 mr-3">
                        <highlight.icon className="h-6 w-6 text-primary" />
                      </div>
                      <h4 className="font-semibold">{highlight.title}</h4>
                    </div>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {highlight.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

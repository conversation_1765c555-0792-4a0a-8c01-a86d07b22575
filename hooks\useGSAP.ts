"use client";

import { useEffect, useRef, RefObject } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export interface GSAPAnimationOptions {
  trigger?: string | Element;
  start?: string;
  end?: string;
  scrub?: boolean | number;
  pin?: boolean;
  markers?: boolean;
  toggleActions?: string;
  onEnter?: () => void;
  onLeave?: () => void;
  onEnterBack?: () => void;
  onLeaveBack?: () => void;
}

export interface GSAPAnimation {
  from?: gsap.TweenVars;
  to?: gsap.TweenVars;
  duration?: number;
  delay?: number;
  ease?: string;
  stagger?: number | object;
}

/**
 * Custom hook for GSAP animations with ScrollTrigger
 */
export const useGSAP = () => {
  const timelineRef = useRef<gsap.core.Timeline | null>(null);

  useEffect(() => {
    // Cleanup function
    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  /**
   * Create a fade in animation with scroll trigger
   */
  const fadeIn = (
    element: string | Element | RefObject<Element>,
    options: GSAPAnimationOptions & GSAPAnimation = {}
  ) => {
    const {
      trigger,
      start = "top 80%",
      end = "bottom 20%",
      toggleActions = "play none none reverse",
      duration = 1,
      delay = 0,
      ease = "power2.out",
      ...animationProps
    } = options;

    const target = typeof element === 'object' && 'current' in element ? element.current : element;
    if (!target) return;

    gsap.fromTo(target, 
      {
        opacity: 0,
        y: 50,
        ...animationProps.from
      },
      {
        opacity: 1,
        y: 0,
        duration,
        delay,
        ease,
        scrollTrigger: {
          trigger: trigger || target,
          start,
          end,
          toggleActions,
        },
        ...animationProps.to
      }
    );
  };

  /**
   * Create a slide in animation with scroll trigger
   */
  const slideIn = (
    element: string | Element | RefObject<Element>,
    direction: 'left' | 'right' | 'up' | 'down' = 'up',
    options: GSAPAnimationOptions & GSAPAnimation = {}
  ) => {
    const {
      trigger,
      start = "top 80%",
      toggleActions = "play none none reverse",
      duration = 1,
      delay = 0,
      ease = "power2.out",
      ...animationProps
    } = options;

    const target = typeof element === 'object' && 'current' in element ? element.current : element;
    if (!target) return;

    const directionMap = {
      left: { x: -100, y: 0 },
      right: { x: 100, y: 0 },
      up: { x: 0, y: 50 },
      down: { x: 0, y: -50 }
    };

    const { x, y } = directionMap[direction];

    gsap.fromTo(target,
      {
        opacity: 0,
        x,
        y,
        ...animationProps.from
      },
      {
        opacity: 1,
        x: 0,
        y: 0,
        duration,
        delay,
        ease,
        scrollTrigger: {
          trigger: trigger || target,
          start,
          toggleActions,
        },
        ...animationProps.to
      }
    );
  };

  /**
   * Create a staggered animation for multiple elements
   */
  const staggerAnimation = (
    elements: string | NodeList | Element[],
    options: GSAPAnimationOptions & GSAPAnimation = {}
  ) => {
    const {
      trigger,
      start = "top 80%",
      toggleActions = "play none none reverse",
      duration = 0.8,
      stagger = 0.1,
      ease = "power2.out",
      ...animationProps
    } = options;

    gsap.fromTo(elements,
      {
        opacity: 0,
        y: 30,
        scale: 0.9,
        ...animationProps.from
      },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration,
        ease,
        stagger,
        scrollTrigger: {
          trigger,
          start,
          toggleActions,
        },
        ...animationProps.to
      }
    );
  };

  /**
   * Create a parallax effect
   */
  const parallax = (
    element: string | Element | RefObject<Element>,
    speed: number = 0.5,
    options: GSAPAnimationOptions = {}
  ) => {
    const {
      trigger,
      start = "top bottom",
      end = "bottom top",
      scrub = true,
    } = options;

    const target = typeof element === 'object' && 'current' in element ? element.current : element;
    if (!target) return;

    gsap.to(target, {
      yPercent: -50 * speed,
      ease: "none",
      scrollTrigger: {
        trigger: trigger || target,
        start,
        end,
        scrub,
      }
    });
  };

  /**
   * Create a timeline for complex animations
   */
  const createTimeline = (options: GSAPAnimationOptions = {}) => {
    const {
      trigger,
      start = "top 80%",
      toggleActions = "play none none reverse",
    } = options;

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger,
        start,
        toggleActions,
      }
    });

    timelineRef.current = tl;
    return tl;
  };

  /**
   * Smooth scroll to element
   */
  const scrollTo = (target: string | Element | number, duration: number = 1) => {
    if (typeof target === 'number') {
      window.scrollTo({
        top: target,
        behavior: 'smooth'
      });
    } else if (typeof target === 'string') {
      const element = document.querySelector(target);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    } else {
      (target as Element).scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return {
    fadeIn,
    slideIn,
    staggerAnimation,
    parallax,
    createTimeline,
    scrollTo,
    gsap,
    ScrollTrigger
  };
};

/**
 * Hook for scroll-triggered animations that automatically cleanup
 */
export const useScrollAnimation = (
  callback: () => void,
  dependencies: any[] = []
) => {
  useEffect(() => {
    callback();

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, dependencies);
};

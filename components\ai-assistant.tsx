"use client";

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Mi<PERSON>, MicO<PERSON>, X, Send, Volume2, VolumeX } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useGSAP } from "@/hooks/useGSAP";
import { speakText, checkElevenLabsStatus } from "@/lib/elevenlabs";

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export default function AIAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const [mode, setMode] = useState<'voice' | 'chat'>('voice'); // Default to voice mode
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState("");
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const [synthesis, setSynthesis] = useState<SpeechSynthesis | null>(null);
  const [elevenLabsAvailable, setElevenLabsAvailable] = useState(false);
  const { fadeIn } = useGSAP();
  const assistantRef = useRef<HTMLDivElement>(null);
  const messagesRef = useRef<HTMLDivElement>(null);

  // Saurabh's portfolio data for AI responses
  const portfolioData = {
    name: "Saurabh Dahariya",
    role: "Full Stack Developer",
    location: "Bengaluru",
    email: "<EMAIL>",
    phone: "8319130513",
    skills: {
      frontend: ["React.js", "Next.js", "TypeScript", "HTML5", "CSS", "Bootstrap", "Tailwind CSS", "GSAP", "Framer Motion", "ShadCN UI"],
      backend: ["JavaScript", "Node.js", "Express.js", "MongoDB", "PostgreSQL", "Firebase"],
      tools: ["Git", "GitHub", "OpenAI API", "Google Maps API", "C++", "MySQL"]
    },
    projects: [
      {
        name: "QuizByAI",
        url: "https://quizbyai.vercel.app",
        description: "AI-powered quiz platform using OpenAI with user login and custom quizzes",
        tech: ["ReactJS", "Firebase", "OpenAI API", "Tailwind CSS", "ShadCN UI"]
      },
      {
        name: "NaturalSQL (Text-to-SQL)",
        url: "https://text-to-query-ai.onrender.com/",
        description: "AI tool that converts natural language to SQL using OpenAI",
        tech: ["ReactJS", "Node.js", "OpenAI API", "Tailwind CSS", "ShadCN UI"]
      },
      {
        name: "Route Tracker",
        url: "https://saurabhd.vercel.app/map",
        description: "Real-time location tracking app with Google Maps integration",
        tech: ["ReactJS", "Redux", "Google Maps API", "Tailwind CSS"]
      }
    ],
    education: "B.Tech in Information Technology from Bhilai Institute of Technology, Durg (2019–2023). Studied at Jawahar Navodaya Vidyalaya, Korba (Class 6-10), then Maths + Computer Science in 11th-12th where I learned C++ and MySQL.",
    training: "MERN Stack Development at JSpider, BTM Layout, Bengaluru (Sep 2024 – Feb 2025)"
  };

  useEffect(() => {
    // Initialize Speech Recognition
    if (typeof window !== 'undefined' && 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      recognitionInstance.continuous = false;
      recognitionInstance.interimResults = false;
      recognitionInstance.lang = 'en-US';

      recognitionInstance.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setIsListening(false);
        handleUserInput(transcript);
      };

      recognitionInstance.onerror = (event) => {
        if (event.error !== 'no-speech') {
          console.error('Speech recognition error:', event.error);
        }
        setIsListening(false);
      };

      recognitionInstance.onend = () => {
        setIsListening(false);
      };

      recognitionInstance.onstart = () => {
        setIsListening(true);
      };

      setRecognition(recognitionInstance);
    }

    // Initialize Speech Synthesis (fallback)
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      const synth = window.speechSynthesis;
      setSynthesis(synth);

      // Ensure voices are loaded
      const loadVoices = () => {
        const voices = synth.getVoices();
        console.log('Speech synthesis voices loaded:', voices.length);
      };

      // Load voices immediately if available
      loadVoices();

      // Also listen for voices changed event
      synth.onvoiceschanged = loadVoices;
    }

    // Check ElevenLabs availability
    checkElevenLabsStatus().then(setElevenLabsAvailable);
  }, []); // Empty dependency array to run only once

  // Auto-start listening when voice mode is opened (only once)
  useEffect(() => {
    if (isOpen && mode === 'voice' && recognition && !isListening && !isSpeaking) {
      const timer = setTimeout(() => {
        if (!isListening && !isSpeaking) { // Double check before starting
          startListening();
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isOpen, mode]); // Only depend on isOpen and mode to prevent loops

  // Separate useEffect for GSAP animation
  useEffect(() => {
    if (assistantRef.current && isOpen) {
      fadeIn(assistantRef.current, {
        duration: 0.5,
        from: { opacity: 0, scale: 0.9 },
        to: { opacity: 1, scale: 1 }
      });
    }
  }, [isOpen]); // Only depend on isOpen, not fadeIn

  const generateAIResponse = async (userInput: string): Promise<string> => {
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: userInput }),
      });

      if (!response.ok) {
        throw new Error('API request failed');
      }

      const data = await response.json();
      let aiResponse = data.response;

      // Check for navigation commands in the response
      if (aiResponse.includes('NAVIGATE:')) {
        const navigationMatch = aiResponse.match(/NAVIGATE:(\/\w+|\/)$/);
        if (navigationMatch) {
          const section = navigationMatch[1];
          // Remove the navigation command from the response
          aiResponse = aiResponse.replace(/\s*NAVIGATE:\/?\w*\s*$/, '');

          // Navigate after a short delay
          setTimeout(() => {
            if (section === '/') {
              scrollToSection('home');
            } else {
              scrollToSection(section.substring(1)); // Remove the leading slash
            }
          }, 1000);
        }
      }

      return aiResponse;

    } catch (error) {
      console.error('Error calling OpenAI API:', error);

      // Fallback to local responses if API fails
      return generateFallbackResponse(userInput);
    }
  };

  const generateFallbackResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();

    // Navigation commands
    if (input.includes('project') || input.includes('work')) {
      setTimeout(() => scrollToSection('projects'), 1000);
      return `I'll show you my projects. I've worked on Route Tracker and Camping Grounds. Let me navigate to the projects section for you.`;
    }

    if (input.includes('skill') || input.includes('technology') || input.includes('tech')) {
      setTimeout(() => scrollToSection('skills'), 1000);
      return `I'm skilled in React.js, Next.js, Node.js, and MongoDB for full-stack development. Let me show you the skills section.`;
    }

    if (input.includes('resume') || input.includes('education') || input.includes('experience') || input.includes('about')) {
      setTimeout(() => scrollToSection('about'), 1000);
      return `I have a B.Tech in Information Technology and I'm currently doing MERN Stack training. Let me navigate to the about section for more details.`;
    }

    if (input.includes('contact') || input.includes('email') || input.includes('phone') || input.includes('reach')) {
      setTimeout(() => scrollToSection('contact'), 1000);
      return `You can contact me at ${portfolioData.email} or call ${portfolioData.phone}. I'm based in ${portfolioData.location}. Let me take you to the contact section.`;
    }

    // Default responses
    const responses = [
      `Hi! I'm Saurabh, a full-stack developer from ${portfolioData.location}. What would you like to know about my work?`,
      `I can help you learn about my projects, skills, or experience. What interests you most?`,
      `I specialize in React, Node.js, and modern web development. Would you like to see my projects or skills?`,
      `Feel free to ask about my technical expertise, projects, or how to get in touch with me.`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const speakResponse = async (text: string) => {
    try {
      setIsSpeaking(true);
      console.log('Speaking text:', text);
      console.log('ElevenLabs available:', elevenLabsAvailable);

      if (elevenLabsAvailable) {
        try {
          // Use ElevenLabs for high-quality voice
          console.log('Using ElevenLabs voice...');
          await speakText(text, {
            voice_settings: {
              stability: 0.6,
              similarity_boost: 0.8,
              style: 0.2,
              use_speaker_boost: true
            }
          });
          console.log('ElevenLabs speech completed');
        } catch (elevenError) {
          console.error('ElevenLabs error:', elevenError);
          // Fallback to browser TTS if ElevenLabs fails
          await fallbackToTTS(text);
        }
      } else {
        console.log('Using browser TTS fallback...');
        await fallbackToTTS(text);
      }

      setIsSpeaking(false);

      // Auto-restart listening in voice mode after speaking (only if modal is still open)
      if (mode === 'voice' && recognition && isOpen) {
        setTimeout(() => {
          if (mode === 'voice' && isOpen && !isListening && !isSpeaking) {
            startListening();
          }
        }, 1500); // Longer delay for natural conversation flow
      }
    } catch (error) {
      console.error('Speech error:', error);
      setIsSpeaking(false);
    }
  };

  const fallbackToTTS = (text: string): Promise<void> => {
    return new Promise((resolve) => {
      if (synthesis && typeof window !== 'undefined' && 'speechSynthesis' in window) {
        try {
          const utterance = new SpeechSynthesisUtterance(text);
          utterance.rate = 0.9;
          utterance.pitch = 1;
          utterance.volume = 1;

          // Try to use a male voice
          const voices = synthesis.getVoices();
          if (voices.length > 0) {
            const maleVoice = voices.find(voice =>
              voice.name.toLowerCase().includes('male') ||
              voice.name.toLowerCase().includes('david') ||
              voice.name.toLowerCase().includes('alex')
            );

            if (maleVoice) {
              utterance.voice = maleVoice;
            }
          }

          utterance.onend = () => {
            resolve();
            // Auto-restart listening in voice mode after TTS
            if (mode === 'voice' && recognition && isOpen) {
              setTimeout(() => {
                if (mode === 'voice' && isOpen && !isListening && !isSpeaking) {
                  startListening();
                }
              }, 1500);
            }
          };
          utterance.onerror = (error) => {
            console.error('TTS error:', error);
            resolve(); // Resolve anyway to prevent hanging
          };

          synthesis.speak(utterance);
        } catch (error) {
          console.error('TTS setup error:', error);
          resolve(); // Resolve anyway to prevent hanging
        }
      } else {
        console.warn('Speech synthesis not available, skipping voice response');
        resolve(); // Resolve anyway to prevent hanging
      }
    });
  };

  const handleUserInput = async (text: string) => {
    if (!text.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: text,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);

    // Add typing indicator
    const typingMessage: Message = {
      id: 'typing',
      text: 'Thinking...',
      isUser: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, typingMessage]);

    try {
      // Generate AI response
      const aiResponse = await generateAIResponse(text);

      // Remove typing indicator and add actual response
      setMessages(prev => {
        const filtered = prev.filter(msg => msg.id !== 'typing');
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: aiResponse,
          isUser: false,
          timestamp: new Date()
        };
        return [...filtered, aiMessage];
      });

      // Only speak the response in voice mode
      if (aiResponse && mode === 'voice') {
        await speakResponse(aiResponse);
      }
    } catch (error) {
      console.error('Error generating response:', error);

      // Remove typing indicator and add error message
      setMessages(prev => {
        const filtered = prev.filter(msg => msg.id !== 'typing');
        const errorMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: "I&apos;m sorry, I&apos;m having trouble processing that right now. Please try again.",
          isUser: false,
          timestamp: new Date()
        };
        return [...filtered, errorMessage];
      });
    }

    setInputText("");
  };

  const startListening = () => {
    if (recognition && !isListening && !isSpeaking) {
      try {
        // Ensure recognition is stopped before starting
        recognition.abort();
        setTimeout(() => {
          if (!isListening && !isSpeaking) {
            recognition.start();
          }
        }, 100);
      } catch (error) {
        console.error('Error starting recognition:', error);
        setIsListening(false);
      }
    }
  };

  const stopListening = () => {
    if (recognition && isListening) {
      recognition.stop();
      // isListening will be set to false in onend event
    }
  };

  const toggleListening = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };



  return (
    <>
      {/* AI Assistant Toggle Button */}
      <motion.div
        className={`fixed bottom-24 right-8 z-40 ${isOpen ? 'hidden' : 'block'}`}
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 2, type: "spring", stiffness: 260, damping: 20 }}
      >
        {/* Pulsing Ring Animation */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary to-purple-600 animate-ping opacity-20"></div>
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary to-purple-600 animate-pulse opacity-30"></div>

        {/* Main Button */}
        <motion.button
          onClick={() => {
            setIsOpen(true);
            setMode('voice'); // Default to voice mode
          }}
          className="relative p-4 bg-gradient-to-r from-primary to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all group"
          whileHover={{
            scale: 1.1,
            boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
          }}
          whileTap={{ scale: 0.95 }}
        >
          <Mic className="h-6 w-6 group-hover:scale-110 transition-transform duration-300" />

          {/* Floating Badge */}
          <motion.div
            className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full shadow-lg"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 3, type: "spring", stiffness: 500, damping: 30 }}
          >
            AI
          </motion.div>
        </motion.button>

        {/* Tooltip */}
        <motion.div
          className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          initial={{ opacity: 0, y: 10 }}
          whileHover={{ opacity: 1, y: 0 }}
        >
          Talk to Saurabh
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </motion.div>
      </motion.div>

      {/* AI Assistant Chat Interface */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={assistantRef}
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="fixed bottom-8 right-8 z-50 w-96 max-w-[calc(100vw-2rem)]"
          >
            <Card className="shadow-2xl border-primary/20">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${isListening ? 'bg-red-500 animate-pulse' : isSpeaking ? 'bg-blue-500 animate-pulse' : 'bg-green-500'}`} />
                    <div>
                      <CardTitle className="text-lg">
                        {mode === 'voice' ? 'Voice Chat' : 'Chat with Saurabh'}
                      </CardTitle>
                      <p className="text-xs text-muted-foreground">
                        {isListening ? 'Listening...' : isSpeaking ? 'Speaking...' : 'Ready to chat'}
                      </p>
                    </div>
                    {isSpeaking && <Volume2 className="h-4 w-4 text-primary animate-pulse" />}
                  </div>
                  <div className="flex items-center gap-2">
                    {/* Mode Toggle */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newMode = mode === 'voice' ? 'chat' : 'voice';
                        if (newMode === 'chat' && isListening) {
                          stopListening();
                        }
                        setMode(newMode);
                      }}
                      className="text-xs"
                    >
                      {mode === 'voice' ? '💬' : '🎤'}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        // Stop all voice activities when closing
                        if (isListening && recognition) {
                          recognition.abort();
                          setIsListening(false);
                        }
                        if (isSpeaking && synthesis) {
                          synthesis.cancel();
                          setIsSpeaking(false);
                        }
                        setIsOpen(false);
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Badge variant="secondary" className="text-xs">
                    {mode === 'voice' ? '🎤 Voice Mode' : '💬 Chat Mode'}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {elevenLabsAvailable ? '✨ ElevenLabs' : '🔊 Browser TTS'}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {mode === 'voice' ? (
                  /* Voice Mode Interface */
                  <div className="h-64 flex flex-col items-center justify-center space-y-6">
                    {/* Enhanced Waveform Visualization */}
                    <div className="flex items-center justify-center space-x-1 h-16">
                      {[...Array(12)].map((_, i) => (
                        <motion.div
                          key={i}
                          className={`w-1 rounded-full transition-all duration-150 ${
                            isListening
                              ? 'bg-red-500'
                              : isSpeaking
                              ? 'bg-blue-500'
                              : 'bg-primary/30'
                          }`}
                          animate={{
                            height: isListening || isSpeaking
                              ? [20, 40 + Math.random() * 20, 20]
                              : 20,
                            opacity: isListening || isSpeaking ? [0.7, 1, 0.7] : 0.3
                          }}
                          transition={{
                            duration: 0.5 + Math.random() * 0.5,
                            repeat: isListening || isSpeaking ? Infinity : 0,
                            ease: "easeInOut",
                            delay: i * 0.1
                          }}
                        />
                      ))}
                    </div>

                    {/* Voice Status */}
                    <div className="text-center space-y-3">
                      <motion.h3
                        className="text-xl font-semibold"
                        animate={{
                          color: isListening ? '#ef4444' : isSpeaking ? '#3b82f6' : '#6366f1'
                        }}
                      >
                        {isListening ? '🎤 Listening...' : isSpeaking ? '🗣️ Speaking...' : '💬 Ready to chat'}
                      </motion.h3>
                      <p className="text-sm text-muted-foreground max-w-xs mx-auto">
                        {isListening
                          ? 'I&apos;m listening to your question. Speak clearly!'
                          : isSpeaking
                          ? 'Playing AI response with ElevenLabs voice...'
                          : 'Click the microphone below to start a voice conversation'
                        }
                      </p>
                      {(isListening || isSpeaking) && (
                        <motion.div
                          className="flex items-center justify-center gap-2 text-xs text-muted-foreground"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                        >
                          <div className={`w-2 h-2 rounded-full ${isListening ? 'bg-red-500' : 'bg-blue-500'} animate-pulse`} />
                          <span>{isListening ? 'Recording audio...' : 'Playing response...'}</span>
                        </motion.div>
                      )}
                    </div>

                    {/* Large Mic Button */}
                    <motion.button
                      onClick={() => {
                        if (isSpeaking) {
                          // Stop speaking if currently speaking
                          if (synthesis) {
                            synthesis.cancel();
                            setIsSpeaking(false);
                          }
                        } else if (isListening) {
                          // Stop listening if currently listening
                          stopListening();
                        } else {
                          // Start listening if idle
                          startListening();
                        }
                      }}
                      whileHover={{ scale: isSpeaking ? 1 : 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`relative p-8 rounded-full text-white shadow-2xl transition-all duration-300 ${
                        isListening
                          ? 'bg-red-500 hover:bg-red-600 shadow-red-500/25'
                          : isSpeaking
                          ? 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/25'
                          : 'bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 shadow-primary/25'
                      }`}
                    >
                      {/* Pulse ring for active states */}
                      {(isListening || isSpeaking) && (
                        <div className={`absolute inset-0 rounded-full animate-ping ${
                          isListening ? 'bg-red-500' : 'bg-blue-500'
                        } opacity-20`} />
                      )}

                      {isSpeaking ? (
                        <Volume2 className="h-10 w-10 animate-pulse relative z-10" />
                      ) : isListening ? (
                        <motion.div
                          animate={{ scale: [1, 1.1, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                          className="relative z-10"
                        >
                          <Mic className="h-10 w-10" />
                        </motion.div>
                      ) : (
                        <Mic className="h-10 w-10 relative z-10" />
                      )}
                    </motion.button>

                    {/* Action hint */}
                    <p className="text-xs text-muted-foreground text-center">
                      {isListening
                        ? 'Click to stop listening'
                        : isSpeaking
                        ? 'Click to stop speaking'
                        : 'Click to start voice chat'
                      }
                    </p>
                  </div>
                ) : (
                  /* Chat Mode Interface */
                  <>
                    {/* Messages */}
                    <div ref={messagesRef} className="h-64 overflow-y-auto space-y-3 pr-2">
                      {messages.length === 0 && (
                        <div className="text-center text-muted-foreground text-sm py-8">
                          <p>👋 Hi! I&apos;m Saurabh. Ask me anything about my projects, skills, or experience!</p>
                        </div>
                      )}
                      {messages.map((message) => (
                        <motion.div
                          key={message.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
                        >
                          <div
                            className={`max-w-[80%] p-3 rounded-lg text-sm ${
                              message.isUser
                                ? 'bg-primary text-primary-foreground'
                                : 'bg-muted'
                            }`}
                          >
                            {message.text}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </>
                )}

                {/* Input Area - Only show in chat mode */}
                {mode === 'chat' && (
                  <>
                    <div className="flex gap-2">
                      <Input
                        value={inputText}
                        onChange={(e) => setInputText(e.target.value)}
                        placeholder="Ask me anything about my work..."
                        onKeyPress={(e) => e.key === 'Enter' && handleUserInput(inputText)}
                        className="flex-1"
                      />
                      <Button
                        size="sm"
                        onClick={() => handleUserInput(inputText)}
                        disabled={!inputText.trim()}
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant={isListening ? "destructive" : "outline"}
                        onClick={toggleListening}
                        disabled={!recognition || isSpeaking}
                      >
                        {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                      </Button>

                      {isSpeaking && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            if (synthesis) {
                              synthesis.cancel();
                              setIsSpeaking(false);
                            }
                          }}
                        >
                          <VolumeX className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <p className="text-xs text-muted-foreground text-center">
                      Try: &quot;Tell me about your projects&quot; • &quot;What tech do you use?&quot; • &quot;Are you hiring?&quot;
                    </p>
                  </>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

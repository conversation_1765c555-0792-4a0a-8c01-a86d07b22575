"use client";

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, MessageCircle, X, Send, Volume2, VolumeX } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useGSAP } from "@/hooks/useGSAP";

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export default function AIAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hi! I'm <PERSON><PERSON><PERSON><PERSON>'s AI assistant. You can ask me about his projects, skills, experience, or anything else. Try saying 'Show me his projects' or 'What are his skills?'",
      isUser: false,
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState("");
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const [synthesis, setSynthesis] = useState<SpeechSynthesis | null>(null);
  const { fadeIn, slideIn } = useGSAP();
  const assistantRef = useRef<HTMLDivElement>(null);
  const messagesRef = useRef<HTMLDivElement>(null);

  // Saurabh's portfolio data for AI responses
  const portfolioData = {
    name: "Saurabh Dahariya",
    role: "Full Stack Developer",
    location: "Bengaluru",
    email: "<EMAIL>",
    phone: "8319130513",
    skills: {
      frontend: ["React.js", "Next.js", "TypeScript", "HTML5", "CSS", "Bootstrap", "Tailwind CSS", "GSAP", "Framer Motion"],
      backend: ["JavaScript", "Node.js", "Express.js", "MongoDB", "Mongoose"],
      tools: ["Git", "GitHub", "JIRA", "Postman", "Swagger", "Docker", "AWS"]
    },
    projects: [
      {
        name: "Route Tracker",
        url: "https://saurabhd.vercel.app/map",
        description: "Real-time vehicle tracking app built with React & Redux",
        tech: ["React", "Redux", "JavaScript"]
      },
      {
        name: "Camping Grounds",
        url: "https://campinggrounds.onrender.com/",
        description: "Full-stack web app with MongoDB, Express.js, EJS",
        tech: ["MongoDB", "Express.js", "EJS", "Bootstrap"]
      }
    ],
    education: "B.Tech in Information Technology from Bhilai Institute of Technology, Durg (2019–2023)",
    training: "MERN Stack Development at JSpider, BTM Layout, Bengaluru (Sep 2024 – Feb 2025)"
  };

  useEffect(() => {
    // Initialize Speech Recognition
    if (typeof window !== 'undefined' && 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      recognitionInstance.continuous = false;
      recognitionInstance.interimResults = false;
      recognitionInstance.lang = 'en-US';

      recognitionInstance.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        handleUserInput(transcript);
        setIsListening(false);
      };

      recognitionInstance.onerror = () => {
        setIsListening(false);
      };

      recognitionInstance.onend = () => {
        setIsListening(false);
      };

      setRecognition(recognitionInstance);
    }

    // Initialize Speech Synthesis
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      setSynthesis(window.speechSynthesis);
    }
  }, []); // Empty dependency array to run only once

  // Separate useEffect for GSAP animation
  useEffect(() => {
    if (assistantRef.current && isOpen) {
      fadeIn(assistantRef, {
        duration: 0.5,
        from: { opacity: 0, scale: 0.9 },
        to: { opacity: 1, scale: 1 }
      });
    }
  }, [isOpen]); // Only depend on isOpen, not fadeIn

  const generateAIResponse = async (userInput: string): Promise<string> => {
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: userInput }),
      });

      if (!response.ok) {
        throw new Error('API request failed');
      }

      const data = await response.json();
      let aiResponse = data.response;

      // Check for navigation commands in the response
      if (aiResponse.includes('NAVIGATE:')) {
        const navigationMatch = aiResponse.match(/NAVIGATE:(\/\w+|\/)$/);
        if (navigationMatch) {
          const section = navigationMatch[1];
          // Remove the navigation command from the response
          aiResponse = aiResponse.replace(/\s*NAVIGATE:\/?\w*\s*$/, '');

          // Navigate after a short delay
          setTimeout(() => {
            if (section === '/') {
              scrollToSection('home');
            } else {
              scrollToSection(section.substring(1)); // Remove the leading slash
            }
          }, 1000);
        }
      }

      return aiResponse;

    } catch (error) {
      console.error('Error calling OpenAI API:', error);

      // Fallback to local responses if API fails
      return generateFallbackResponse(userInput);
    }
  };

  const generateFallbackResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();

    // Navigation commands
    if (input.includes('project') || input.includes('work')) {
      setTimeout(() => scrollToSection('projects'), 1000);
      return `I'll show you my projects. I've worked on Route Tracker and Camping Grounds. Let me navigate to the projects section for you.`;
    }

    if (input.includes('skill') || input.includes('technology') || input.includes('tech')) {
      setTimeout(() => scrollToSection('skills'), 1000);
      return `I'm skilled in React.js, Next.js, Node.js, and MongoDB for full-stack development. Let me show you the skills section.`;
    }

    if (input.includes('resume') || input.includes('education') || input.includes('experience') || input.includes('about')) {
      setTimeout(() => scrollToSection('about'), 1000);
      return `I have a B.Tech in Information Technology and I'm currently doing MERN Stack training. Let me navigate to the about section for more details.`;
    }

    if (input.includes('contact') || input.includes('email') || input.includes('phone') || input.includes('reach')) {
      setTimeout(() => scrollToSection('contact'), 1000);
      return `You can contact me at ${portfolioData.email} or call ${portfolioData.phone}. I'm based in ${portfolioData.location}. Let me take you to the contact section.`;
    }

    // Default responses
    const responses = [
      `Hi! I'm Saurabh, a full-stack developer from ${portfolioData.location}. What would you like to know about my work?`,
      `I can help you learn about my projects, skills, or experience. What interests you most?`,
      `I specialize in React, Node.js, and modern web development. Would you like to see my projects or skills?`,
      `Feel free to ask about my technical expertise, projects, or how to get in touch with me.`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleUserInput = async (text: string) => {
    if (!text.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: text,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);

    // Add typing indicator
    const typingMessage: Message = {
      id: 'typing',
      text: 'Thinking...',
      isUser: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, typingMessage]);

    try {
      // Generate AI response
      const aiResponse = await generateAIResponse(text);

      // Remove typing indicator and add actual response
      setMessages(prev => {
        const filtered = prev.filter(msg => msg.id !== 'typing');
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: aiResponse,
          isUser: false,
          timestamp: new Date()
        };
        return [...filtered, aiMessage];
      });

      // Speak the response
      if (synthesis && aiResponse) {
        const utterance = new SpeechSynthesisUtterance(aiResponse);
        utterance.rate = 0.9;
        utterance.pitch = 1;
        utterance.onstart = () => setIsSpeaking(true);
        utterance.onend = () => setIsSpeaking(false);
        synthesis.speak(utterance);
      }
    } catch (error) {
      console.error('Error generating response:', error);

      // Remove typing indicator and add error message
      setMessages(prev => {
        const filtered = prev.filter(msg => msg.id !== 'typing');
        const errorMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: "I'm sorry, I'm having trouble processing that right now. Please try again.",
          isUser: false,
          timestamp: new Date()
        };
        return [...filtered, errorMessage];
      });
    }

    setInputText("");
  };

  const startListening = () => {
    if (recognition) {
      setIsListening(true);
      recognition.start();
    }
  };

  const stopListening = () => {
    if (recognition) {
      recognition.stop();
      setIsListening(false);
    }
  };

  const toggleSpeech = () => {
    if (synthesis) {
      if (isSpeaking) {
        synthesis.cancel();
        setIsSpeaking(false);
      }
    }
  };

  return (
    <>
      {/* AI Assistant Toggle Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-24 right-8 z-40 p-4 bg-gradient-to-r from-primary to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all ${isOpen ? 'hidden' : 'block'}`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 2 }}
      >
        <MessageCircle className="h-6 w-6" />
      </motion.button>

      {/* AI Assistant Chat Interface */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={assistantRef}
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="fixed bottom-8 right-8 z-50 w-96 max-w-[calc(100vw-2rem)]"
          >
            <Card className="shadow-2xl border-primary/20">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                    <CardTitle className="text-lg">AI Assistant</CardTitle>
                    {isSpeaking && <Volume2 className="h-4 w-4 text-primary animate-pulse" />}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex gap-2">
                  <Badge variant="secondary" className="text-xs">Voice Enabled</Badge>
                  <Badge variant="secondary" className="text-xs">Smart Navigation</Badge>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Messages */}
                <div ref={messagesRef} className="h-64 overflow-y-auto space-y-3 pr-2">
                  {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[80%] p-3 rounded-lg text-sm ${
                          message.isUser
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        }`}
                      >
                        {message.text}
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Input Area */}
                <div className="flex gap-2">
                  <Input
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    placeholder="Ask about projects, skills, or experience..."
                    onKeyPress={(e) => e.key === 'Enter' && handleUserInput(inputText)}
                    className="flex-1"
                  />
                  <Button
                    size="sm"
                    onClick={() => handleUserInput(inputText)}
                    disabled={!inputText.trim()}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant={isListening ? "destructive" : "outline"}
                    onClick={isListening ? stopListening : startListening}
                    disabled={!recognition}
                  >
                    {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                  </Button>
                  {isSpeaking && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={toggleSpeech}
                    >
                      <VolumeX className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <p className="text-xs text-muted-foreground text-center">
                  Try: "Show me projects" • "What are his skills?" • "Contact info"
                </p>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

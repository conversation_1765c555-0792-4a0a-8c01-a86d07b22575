"use client";

import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

/**
 * Common animation presets
 */
export const animationPresets = {
  // Fade animations
  fadeIn: {
    from: { opacity: 0, y: 30 },
    to: { opacity: 1, y: 0, duration: 1, ease: "power2.out" }
  },
  
  fadeInUp: {
    from: { opacity: 0, y: 50 },
    to: { opacity: 1, y: 0, duration: 1, ease: "power2.out" }
  },
  
  fadeInDown: {
    from: { opacity: 0, y: -50 },
    to: { opacity: 1, y: 0, duration: 1, ease: "power2.out" }
  },
  
  fadeInLeft: {
    from: { opacity: 0, x: -50 },
    to: { opacity: 1, x: 0, duration: 1, ease: "power2.out" }
  },
  
  fadeInRight: {
    from: { opacity: 0, x: 50 },
    to: { opacity: 1, x: 0, duration: 1, ease: "power2.out" }
  },

  // Scale animations
  scaleIn: {
    from: { opacity: 0, scale: 0.8 },
    to: { opacity: 1, scale: 1, duration: 0.8, ease: "back.out(1.7)" }
  },

  // Slide animations
  slideInUp: {
    from: { y: 100, opacity: 0 },
    to: { y: 0, opacity: 1, duration: 1, ease: "power3.out" }
  },

  slideInDown: {
    from: { y: -100, opacity: 0 },
    to: { y: 0, opacity: 1, duration: 1, ease: "power3.out" }
  },

  // Rotation animations
  rotateIn: {
    from: { rotation: -180, opacity: 0, scale: 0.5 },
    to: { rotation: 0, opacity: 1, scale: 1, duration: 1, ease: "back.out(1.7)" }
  }
};

/**
 * Scroll trigger configurations
 */
export const scrollTriggerConfigs = {
  default: {
    start: "top 80%",
    end: "bottom 20%",
    toggleActions: "play none none reverse"
  },
  
  immediate: {
    start: "top 90%",
    toggleActions: "play none none reverse"
  },
  
  delayed: {
    start: "top 60%",
    toggleActions: "play none none reverse"
  },
  
  scrub: {
    start: "top bottom",
    end: "bottom top",
    scrub: 1
  },
  
  pin: {
    start: "top top",
    end: "bottom top",
    pin: true,
    scrub: 1
  }
};

/**
 * Initialize smooth scrolling
 */
export const initSmoothScroll = () => {
  if (typeof window === 'undefined') return;

  // Enable smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href') as string);
      if (target) {
        gsap.to(window, {
          duration: 1,
          scrollTo: target,
          ease: "power2.inOut"
        });
      }
    });
  });
};

/**
 * Create a reveal animation for text
 */
export const revealText = (
  element: string | Element,
  options: {
    duration?: number;
    delay?: number;
    stagger?: number;
    trigger?: string | Element;
  } = {}
) => {
  const { duration = 1, delay = 0, stagger = 0.1, trigger } = options;
  
  // Split text into words or characters for animation
  const target = typeof element === 'string' ? document.querySelector(element) : element;
  if (!target) return;

  const text = target.textContent || '';
  const words = text.split(' ');
  
  target.innerHTML = words.map(word => 
    `<span class="word" style="display: inline-block; overflow: hidden;">
      <span style="display: inline-block;">${word}</span>
    </span>`
  ).join(' ');

  const wordElements = target.querySelectorAll('.word span');

  gsap.fromTo(wordElements, 
    {
      y: 100,
      opacity: 0
    },
    {
      y: 0,
      opacity: 1,
      duration,
      delay,
      stagger,
      ease: "power2.out",
      scrollTrigger: {
        trigger: trigger || target,
        start: "top 80%",
        toggleActions: "play none none reverse"
      }
    }
  );
};

/**
 * Create a magnetic hover effect
 */
export const magneticHover = (element: string | Element, strength: number = 0.3) => {
  const target = typeof element === 'string' ? document.querySelector(element) : element;
  if (!target) return;

  const handleMouseMove = (e: MouseEvent) => {
    const rect = (target as Element).getBoundingClientRect();
    const x = e.clientX - rect.left - rect.width / 2;
    const y = e.clientY - rect.top - rect.height / 2;

    gsap.to(target, {
      x: x * strength,
      y: y * strength,
      duration: 0.3,
      ease: "power2.out"
    });
  };

  const handleMouseLeave = () => {
    gsap.to(target, {
      x: 0,
      y: 0,
      duration: 0.5,
      ease: "elastic.out(1, 0.3)"
    });
  };

  target.addEventListener('mousemove', handleMouseMove);
  target.addEventListener('mouseleave', handleMouseLeave);

  // Return cleanup function
  return () => {
    target.removeEventListener('mousemove', handleMouseMove);
    target.removeEventListener('mouseleave', handleMouseLeave);
  };
};

/**
 * Create a loading animation
 */
export const createLoadingAnimation = () => {
  const tl = gsap.timeline();
  
  // Add your loading animation here
  tl.from('.loading-element', {
    scale: 0,
    opacity: 0,
    duration: 0.5,
    stagger: 0.1,
    ease: "back.out(1.7)"
  });

  return tl;
};

/**
 * Create a page transition animation
 */
export const pageTransition = {
  enter: (element: string | Element) => {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    return gsap.fromTo(target, 
      {
        opacity: 0,
        y: 50
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out"
      }
    );
  },

  exit: (element: string | Element) => {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    return gsap.to(target, {
      opacity: 0,
      y: -50,
      duration: 0.5,
      ease: "power2.in"
    });
  }
};

/**
 * Utility to refresh ScrollTrigger (useful after dynamic content changes)
 */
export const refreshScrollTrigger = () => {
  ScrollTrigger.refresh();
};

/**
 * Kill all ScrollTrigger instances (useful for cleanup)
 */
export const killAllScrollTriggers = () => {
  ScrollTrigger.getAll().forEach(trigger => trigger.kill());
};

/**
 * Create a progress bar animation
 */
export const createProgressBar = (element: string | Element, progress: number) => {
  const target = typeof element === 'string' ? document.querySelector(element) : element;
  if (!target) return;

  gsap.to(target, {
    width: `${progress}%`,
    duration: 1,
    ease: "power2.out"
  });
};

/**
 * Create a counter animation
 */
export const animateCounter = (
  element: string | Element, 
  endValue: number, 
  duration: number = 2
) => {
  const target = typeof element === 'string' ? document.querySelector(element) : element;
  if (!target) return;

  const obj = { value: 0 };
  
  gsap.to(obj, {
    value: endValue,
    duration,
    ease: "power2.out",
    onUpdate: () => {
      (target as Element).textContent = Math.round(obj.value).toString();
    }
  });
};

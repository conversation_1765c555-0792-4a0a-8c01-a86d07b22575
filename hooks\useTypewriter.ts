"use client";

import { useState, useEffect } from 'react';

interface UseTypewriterOptions {
  words: string[];
  loop?: boolean;
  delaySpeed?: number;
  deleteSpeed?: number;
  typeSpeed?: number;
}

export function useTypewriter({
  words,
  loop = true,
  delaySpeed = 2000,
  deleteSpeed = 50,
  typeSpeed = 100,
}: UseTypewriterOptions) {
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [currentText, setCurrentText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [isWaiting, setIsWaiting] = useState(false);

  useEffect(() => {
    const currentWord = words[currentWordIndex];
    
    if (isWaiting) {
      const waitTimer = setTimeout(() => {
        setIsWaiting(false);
        setIsDeleting(true);
      }, delaySpeed);
      
      return () => clearTimeout(waitTimer);
    }

    if (isDeleting) {
      if (currentText === '') {
        setIsDeleting(false);
        setCurrentWordIndex((prev) => (prev + 1) % words.length);
        return;
      }
      
      const deleteTimer = setTimeout(() => {
        setCurrentText(currentWord.substring(0, currentText.length - 1));
      }, deleteSpeed);
      
      return () => clearTimeout(deleteTimer);
    } else {
      if (currentText === currentWord) {
        if (loop || currentWordIndex < words.length - 1) {
          setIsWaiting(true);
        }
        return;
      }
      
      const typeTimer = setTimeout(() => {
        setCurrentText(currentWord.substring(0, currentText.length + 1));
      }, typeSpeed);
      
      return () => clearTimeout(typeTimer);
    }
  }, [currentText, currentWordIndex, isDeleting, isWaiting, words, loop, delaySpeed, deleteSpeed, typeSpeed]);

  return {
    text: currentText,
    isDeleting,
    isWaiting,
  };
}

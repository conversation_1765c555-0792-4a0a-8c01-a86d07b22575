"use client";

import { useState, useEffect } from "react";
import { ArrowUp } from "lucide-react";
import { useGS<PERSON> } from "@/hooks/useGSAP";
import { gsap } from "gsap";

export default function GSAPScrollToTop() {
  const [isVisible, setIsVisible] = useState(false);
  const { scrollTo } = useGSAP();

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);

    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const handleScrollToTop = () => {
    scrollTo(0, 1.5);
  };

  useEffect(() => {
    const button = document.querySelector('.scroll-to-top-btn');
    if (button) {
      if (isVisible) {
        gsap.to(button, {
          opacity: 1,
          scale: 1,
          duration: 0.3,
          ease: "back.out(1.7)"
        });
      } else {
        gsap.to(button, {
          opacity: 0,
          scale: 0.8,
          duration: 0.3,
          ease: "power2.in"
        });
      }
    }
  }, [isVisible]);

  return (
    <button
      className={`scroll-to-top-btn fixed bottom-8 right-8 z-50 p-3 bg-primary text-primary-foreground rounded-full shadow-lg hover:shadow-xl transition-shadow opacity-0 scale-80`}
      onClick={handleScrollToTop}
      aria-label="Scroll to top"
    >
      <ArrowUp className="h-5 w-5" />
    </button>
  );
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Force dark mode variables immediately */
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    color-scheme: dark;
  }

  body {
    @apply bg-background text-foreground;
    position: relative;
    overflow-x: hidden;
    min-height: 100vh;
    background-image:
      radial-gradient(circle at 25% 25%, hsl(var(--primary) / 0.05) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, hsl(var(--primary) / 0.03) 0%, transparent 50%);
    background-attachment: fixed;
  }

  /* Force dark mode immediately */
  html.dark,
  html[data-theme="dark"],
  html {
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;
  }
}

@layer components {
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  .dark .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }

  .tech-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
  }

  .tech-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 15% 50%, hsl(var(--primary) / 0.1) 0%, transparent 25%),
      radial-gradient(circle at 85% 30%, hsl(var(--primary) / 0.15) 0%, transparent 25%);
    opacity: 0.5;
  }

  .circuit-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      linear-gradient(to right, hsl(var(--primary) / 0.1) 1px, transparent 1px),
      linear-gradient(to bottom, hsl(var(--primary) / 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
  }

  .floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .floating-element {
    position: absolute;
    color: hsl(var(--primary) / 0.2);
    font-family: var(--font-geist-mono);
    animation: float 20s linear infinite;
    white-space: nowrap;
  }

  @keyframes float {
    0% {
      transform: translateY(100vh) translateX(-50%);
      opacity: 0;
    }
    10% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      transform: translateY(-100%) translateX(50%);
      opacity: 0;
    }
  }

  .binary {
    font-size: 14px;
    letter-spacing: 2px;
  }

  .matrix-rain {
    position: absolute;
    color: hsl(var(--primary) / 0.15);
    font-size: 14px;
    line-height: 1;
    transform-origin: 50% 0%;
    animation: matrixRain 10s linear infinite;
  }

  @keyframes matrixRain {
    0% {
      transform: translateY(-100%);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateY(100vh);
      opacity: 0;
    }
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--primary)) hsl(var(--background));
}

*::-webkit-scrollbar {
  width: 8px;
}

*::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

*::-webkit-scrollbar-thumb {
  background-color: hsl(var(--primary));
  border-radius: 20px;
  border: 2px solid hsl(var(--background));
}

*::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--primary) / 0.8);
}

/* Minimal background overlay */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, hsl(217, 91%, 60%) 0%, transparent 70%),
    radial-gradient(circle at 80% 20%, hsl(217, 91%, 60%) 0%, transparent 70%);
  opacity: 0.02;
  pointer-events: none;
  z-index: -1;
}

/* Minimal grid pattern */
.bg-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  background-image:
    linear-gradient(to right, hsl(var(--primary) / 0.03) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--primary) / 0.03) 1px, transparent 1px);
  background-size: 100px 100px;
}

/* Line clamp utilities */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Grid pattern background */
.bg-grid-pattern {
  background-image:
    linear-gradient(to right, hsl(var(--primary) / 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--primary) / 0.05) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Noise texture */
.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.85' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* Animation delays */
.delay-1000 {
  animation-delay: 1s;
}

.delay-2000 {
  animation-delay: 2s;
}

/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-white\/20::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}


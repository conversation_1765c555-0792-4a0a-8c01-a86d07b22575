"use client";

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON>, <PERSON>cO<PERSON>, MessageCircle, X, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

type Mode = 'voice' | 'chat';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export function NewAIAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const [mode, setMode] = useState<Mode>('voice');
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [inputText, setInputText] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      
      recognitionInstance.continuous = false;
      recognitionInstance.interimResults = false;
      recognitionInstance.lang = 'en-US';

      recognitionInstance.onstart = () => setIsListening(true);
      recognitionInstance.onend = () => setIsListening(false);
      
      recognitionInstance.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        handleVoiceInput(transcript);
      };

      recognitionInstance.onerror = (event) => {
        if (event.error !== 'no-speech') {
          console.error('Speech recognition error:', event.error);
        }
        setIsListening(false);
      };

      setRecognition(recognitionInstance);
    }
  }, []);

  // Auto-start listening when voice mode opens
  useEffect(() => {
    if (isOpen && mode === 'voice' && recognition && !isListening && !isSpeaking) {
      const timer = setTimeout(() => {
        startListening();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isOpen, mode]);

  const startListening = () => {
    if (recognition && !isListening && !isSpeaking) {
      try {
        recognition.start();
      } catch (error) {
        console.error('Error starting recognition:', error);
      }
    }
  };

  const stopListening = () => {
    if (recognition && isListening) {
      recognition.stop();
    }
  };

  const handleVoiceInput = async (transcript: string) => {
    console.log('Voice input:', transcript);
    
    try {
      // Get AI response
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: transcript }),
      });

      const data = await response.json();
      
      if (data.response) {
        // Speak the response using ElevenLabs
        await speakResponse(data.response);
        
        // Auto-restart listening after speaking
        setTimeout(() => {
          if (mode === 'voice' && isOpen && !isListening && !isSpeaking) {
            startListening();
          }
        }, 1500);
      }
    } catch (error) {
      console.error('Error processing voice input:', error);
    }
  };

  const handleChatInput = async (message: string) => {
    if (!message.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: message,
      isUser: true,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);
    setInputText('');

    try {
      // Get AI response
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message }),
      });

      const data = await response.json();
      
      if (data.response) {
        // Add AI response
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.response,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error processing chat input:', error);
    }
  };

  const speakResponse = async (text: string) => {
    try {
      setIsSpeaking(true);
      
      const response = await fetch('/api/voice', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text }),
      });

      const data = await response.json();
      
      if (data.success && data.audio) {
        // Play the audio
        const audio = new Audio(`data:audio/mpeg;base64,${data.audio}`);
        audio.onended = () => setIsSpeaking(false);
        audio.onerror = () => setIsSpeaking(false);
        await audio.play();
      } else {
        setIsSpeaking(false);
      }
    } catch (error) {
      console.error('Error speaking response:', error);
      setIsSpeaking(false);
    }
  };

  const closeAssistant = () => {
    if (isListening && recognition) {
      recognition.stop();
    }
    if (isSpeaking) {
      setIsSpeaking(false);
    }
    setIsOpen(false);
    setMessages([]);
  };

  return (
    <>
      {/* Floating AI Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 z-50 p-4 bg-gradient-to-r from-primary to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 2 }}
      >
        <MessageCircle className="h-6 w-6" />
      </motion.button>

      {/* AI Assistant Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-background border border-border rounded-2xl shadow-2xl w-full max-w-md h-[600px] flex flex-col"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-border">
                <div className="flex items-center gap-3">
                  <div className="text-lg font-semibold">Hi, I'm Saurabh! 👋</div>
                </div>
                <div className="flex items-center gap-2">
                  {/* Mode Toggle */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newMode = mode === 'voice' ? 'chat' : 'voice';
                      if (newMode === 'chat' && isListening) {
                        stopListening();
                      }
                      setMode(newMode);
                      setMessages([]);
                    }}
                    className="text-xs"
                  >
                    {mode === 'voice' ? '💬' : '🎤'}
                  </Button>
                  <Button variant="ghost" size="sm" onClick={closeAssistant}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 flex flex-col">
                {mode === 'voice' ? (
                  <VoiceMode 
                    isListening={isListening}
                    isSpeaking={isSpeaking}
                    onToggleListening={() => isListening ? stopListening() : startListening()}
                  />
                ) : (
                  <ChatMode 
                    messages={messages}
                    inputText={inputText}
                    setInputText={setInputText}
                    onSendMessage={handleChatInput}
                  />
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

// Voice Mode Component
function VoiceMode({ isListening, isSpeaking, onToggleListening }: {
  isListening: boolean;
  isSpeaking: boolean;
  onToggleListening: () => void;
}) {
  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8 space-y-8">
      {/* Waveform */}
      <div className="flex items-center justify-center space-x-1 h-16">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className={`w-1 rounded-full transition-all duration-150 ${
              isListening ? 'bg-red-500' : isSpeaking ? 'bg-blue-500' : 'bg-primary/30'
            }`}
            animate={{
              height: isListening || isSpeaking ? [20, 40 + Math.random() * 20, 20] : 20,
              opacity: isListening || isSpeaking ? [0.7, 1, 0.7] : 0.3
            }}
            transition={{
              duration: 0.5 + Math.random() * 0.5,
              repeat: isListening || isSpeaking ? Infinity : 0,
              ease: "easeInOut",
              delay: i * 0.1
            }}
          />
        ))}
      </div>

      {/* Status */}
      <div className="text-center space-y-3">
        <motion.h3 className="text-xl font-semibold">
          {isListening ? '🎤 Listening...' : isSpeaking ? '🗣️ Speaking...' : '💬 Ready to chat'}
        </motion.h3>
        <p className="text-sm text-muted-foreground max-w-xs mx-auto">
          {isListening
            ? 'I\'m listening to your question. Speak clearly!'
            : isSpeaking
            ? 'Playing AI response with ElevenLabs voice...'
            : 'Click the microphone below to start a voice conversation'
          }
        </p>
      </div>

      {/* Mic Button */}
      <motion.button
        onClick={onToggleListening}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className={`relative p-8 rounded-full text-white shadow-2xl transition-all duration-300 ${
          isListening
            ? 'bg-red-500 hover:bg-red-600 shadow-red-500/25'
            : isSpeaking
            ? 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/25'
            : 'bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 shadow-primary/25'
        }`}
      >
        {(isListening || isSpeaking) && (
          <div className={`absolute inset-0 rounded-full animate-ping ${
            isListening ? 'bg-red-500' : 'bg-blue-500'
          } opacity-20`} />
        )}
        
        {isListening ? (
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
            className="relative z-10"
          >
            <Mic className="h-10 w-10" />
          </motion.div>
        ) : (
          <Mic className="h-10 w-10 relative z-10" />
        )}
      </motion.button>

      <p className="text-xs text-muted-foreground text-center">
        {isListening ? 'Click to stop listening' : 'Click to start voice chat'}
      </p>
    </div>
  );
}

// Chat Mode Component  
function ChatMode({ messages, inputText, setInputText, onSendMessage }: {
  messages: Message[];
  inputText: string;
  setInputText: (text: string) => void;
  onSendMessage: (message: string) => void;
}) {
  return (
    <>
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <p>👋 Hi! I'm Saurabh Dahariya</p>
            <p className="text-sm mt-2">Ask me about my projects, skills, or experience!</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] p-3 rounded-lg ${
                  message.isUser
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground'
                }`}
              >
                {message.text}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t border-border">
        <div className="flex gap-2">
          <Input
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && onSendMessage(inputText)}
            placeholder="Ask me anything about my work..."
            className="flex-1"
          />
          <Button
            size="sm"
            onClick={() => onSendMessage(inputText)}
            disabled={!inputText.trim()}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-xs text-muted-foreground text-center mt-2">
          Try: "Tell me about your projects" • "What tech do you use?" • "Are you hiring?"
        </p>
      </div>
    </>
  );
}

"use client";

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON>, <PERSON>cO<PERSON>, MessageCircle, X, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

type Mode = 'voice' | 'chat';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export function NewAIAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const [mode, setMode] = useState<Mode>('voice');
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [inputText, setInputText] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      
      recognitionInstance.continuous = false;
      recognitionInstance.interimResults = false;
      recognitionInstance.lang = 'en-US';

      recognitionInstance.onstart = () => setIsListening(true);
      recognitionInstance.onend = () => setIsListening(false);
      
      recognitionInstance.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        handleVoiceInput(transcript);
      };

      recognitionInstance.onerror = (event) => {
        if (event.error !== 'no-speech' && event.error !== 'aborted') {
          console.error('Speech recognition error:', event.error);
        }
        setIsListening(false);
      };

      setRecognition(recognitionInstance);
    }
  }, []);

  // Auto-start listening when voice mode opens (only once)
  useEffect(() => {
    if (isOpen && mode === 'voice' && recognition && !isListening && !isSpeaking) {
      const timer = setTimeout(() => {
        if (isOpen && mode === 'voice' && !isListening && !isSpeaking) {
          startListening();
        }
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isOpen, mode]); // Only depend on isOpen and mode

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const startListening = () => {
    if (recognition && !isListening && !isSpeaking) {
      try {
        // Ensure recognition is stopped before starting
        recognition.abort();
        setTimeout(() => {
          if (!isListening && !isSpeaking && recognition) {
            recognition.start();
          }
        }, 100);
      } catch (error) {
        console.error('Error starting recognition:', error);
      }
    }
  };

  const stopListening = () => {
    if (recognition && isListening) {
      recognition.stop();
    }
  };

  // Handle navigation commands
  const handleNavigation = (response: string) => {
    console.log('Checking navigation for response:', response);

    if (response.includes('NAVIGATE:/projects')) {
      console.log('Navigating to projects');
      setTimeout(() => {
        document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' });
      }, 500);
    } else if (response.includes('NAVIGATE:/about')) {
      console.log('Navigating to about');
      setTimeout(() => {
        document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' });
      }, 500);
    } else if (response.includes('NAVIGATE:/contact')) {
      console.log('Navigating to contact');
      setTimeout(() => {
        document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
      }, 500);
    } else if (response.includes('NAVIGATE:/skills')) {
      console.log('Navigating to skills');
      setTimeout(() => {
        document.getElementById('skills')?.scrollIntoView({ behavior: 'smooth' });
      }, 500);
    } else if (response.includes('NAVIGATE:/home')) {
      console.log('Navigating to home');
      setTimeout(() => {
        document.getElementById('home')?.scrollIntoView({ behavior: 'smooth' });
      }, 500);
    }
  };

  const handleVoiceInput = async (transcript: string) => {
    console.log('Voice input:', transcript);
    
    try {
      // Get AI response
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: transcript }),
      });

      const data = await response.json();
      
      if (data.response) {
        // Check for navigation commands
        handleNavigation(data.response);

        // Speak the response using ElevenLabs
        await speakResponse(data.response);

        // Auto-restart listening after speaking
        setTimeout(() => {
          if (mode === 'voice' && isOpen && !isListening && !isSpeaking) {
            startListening();
          }
        }, 1500);
      }
    } catch (error) {
      console.error('Error processing voice input:', error);
    }
  };

  const handleChatInput = async (message: string) => {
    if (!message.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: message,
      isUser: true,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    try {
      // Get AI response
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message }),
      });

      const data = await response.json();

      if (data.response) {
        // Check for navigation commands
        handleNavigation(data.response);

        // Add AI response
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.response,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error processing chat input:', error);
      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: "Sorry, I'm having trouble responding right now. Please try again!",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const speakResponse = async (text: string) => {
    try {
      setIsSpeaking(true);
      
      const response = await fetch('/api/voice', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text }),
      });

      const data = await response.json();
      
      if (data.success && data.audio) {
        // Play the audio
        const audio = new Audio(`data:audio/mpeg;base64,${data.audio}`);
        audio.onended = () => setIsSpeaking(false);
        audio.onerror = () => setIsSpeaking(false);
        await audio.play();
      } else {
        setIsSpeaking(false);
      }
    } catch (error) {
      console.error('Error speaking response:', error);
      setIsSpeaking(false);
    }
  };

  const closeAssistant = () => {
    if (isListening && recognition) {
      recognition.stop();
    }
    if (isSpeaking) {
      setIsSpeaking(false);
    }
    setIsOpen(false);
    setMessages([]);
  };

  return (
    <>
      {/* Floating AI Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 z-50 p-4 bg-gradient-to-r from-primary to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all group"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 2 }}
      >
        <div className="flex items-center gap-2">
          <motion.div
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="group-hover:animate-none"
          >
            <MessageCircle className="h-6 w-6" />
          </motion.div>
          <motion.span
            initial={{ opacity: 0, width: 0 }}
            animate={{ opacity: 1, width: "auto" }}
            transition={{ delay: 2.5, duration: 0.5 }}
            className="font-semibold text-sm whitespace-nowrap overflow-hidden"
          >
            AI
          </motion.span>
        </div>
      </motion.button>

      {/* AI Assistant Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 pointer-events-none"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0, x: 100, y: 100 }}
              animate={{ scale: 1, opacity: 1, x: 0, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, x: 100, y: 100 }}
              className="fixed bottom-6 right-6 w-96 h-[500px] bg-background/95 backdrop-blur-xl border border-border/50 rounded-2xl shadow-2xl flex flex-col pointer-events-auto overflow-hidden"
              style={{
                background: 'rgba(0, 0, 0, 0.85)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
              }}
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-border/30">
                <div className="flex items-center gap-3">
                  <div className="text-lg font-semibold text-white">Hi, I'm Saurabh! 👋</div>
                </div>
                <div className="flex items-center gap-2">
                  {/* Stop Button (only in voice mode when active) */}
                  {mode === 'voice' && (isListening || isSpeaking) && (
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        if (isListening) stopListening();
                        if (isSpeaking) setIsSpeaking(false);
                      }}
                      className="text-xs"
                    >
                      ⏹️ Stop
                    </Button>
                  )}
                  {/* Mode Toggle */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newMode = mode === 'voice' ? 'chat' : 'voice';
                      if (newMode === 'chat' && isListening) {
                        stopListening();
                      }
                      if (isSpeaking) setIsSpeaking(false);
                      setMode(newMode);
                      setMessages([]);
                    }}
                    className="text-xs bg-white/10 border-white/20 text-white hover:bg-white/20"
                  >
                    {mode === 'voice' ? '💬' : '🎤'}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={closeAssistant}
                    className="text-white hover:bg-white/10"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 flex flex-col">
                {mode === 'voice' ? (
                  <VoiceMode 
                    isListening={isListening}
                    isSpeaking={isSpeaking}
                    onToggleListening={() => isListening ? stopListening() : startListening()}
                  />
                ) : (
                  <ChatMode
                    messages={messages}
                    inputText={inputText}
                    setInputText={setInputText}
                    onSendMessage={handleChatInput}
                    isTyping={isTyping}
                    messagesEndRef={messagesEndRef}
                  />
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

// Voice Mode Component
function VoiceMode({ isListening, isSpeaking, onToggleListening }: {
  isListening: boolean;
  isSpeaking: boolean;
  onToggleListening: () => void;
}) {
  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8 space-y-8">
      {/* Waveform */}
      <div className="flex items-center justify-center space-x-1 h-16">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className={`w-1 rounded-full transition-all duration-150 ${
              isListening ? 'bg-red-500' : isSpeaking ? 'bg-blue-500' : 'bg-primary/30'
            }`}
            animate={{
              height: isListening || isSpeaking ? [20, 40 + Math.random() * 20, 20] : 20,
              opacity: isListening || isSpeaking ? [0.7, 1, 0.7] : 0.3
            }}
            transition={{
              duration: 0.5 + Math.random() * 0.5,
              repeat: isListening || isSpeaking ? Infinity : 0,
              ease: "easeInOut",
              delay: i * 0.1
            }}
          />
        ))}
      </div>

      {/* Status */}
      <div className="text-center space-y-3">
        <motion.h3 className="text-xl font-semibold text-white">
          {isListening ? '🎤 Listening...' : isSpeaking ? '🗣️ Speaking...' : '💬 Ready to chat'}
        </motion.h3>
        <p className="text-sm text-white/70 max-w-xs mx-auto">
          {isListening
            ? 'I\'m listening to your question. Speak clearly!'
            : isSpeaking
            ? 'Playing AI response with ElevenLabs voice...'
            : 'Click the microphone below to start a voice conversation'
          }
        </p>
      </div>

      {/* Mic Button */}
      <motion.button
        onClick={onToggleListening}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className={`relative p-8 rounded-full text-white shadow-2xl transition-all duration-300 ${
          isListening
            ? 'bg-red-500 hover:bg-red-600 shadow-red-500/25'
            : isSpeaking
            ? 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/25'
            : 'bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 shadow-primary/25'
        }`}
      >
        {(isListening || isSpeaking) && (
          <div className={`absolute inset-0 rounded-full animate-ping ${
            isListening ? 'bg-red-500' : 'bg-blue-500'
          } opacity-20`} />
        )}
        
        {isListening ? (
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
            className="relative z-10"
          >
            <Mic className="h-10 w-10" />
          </motion.div>
        ) : (
          <Mic className="h-10 w-10 relative z-10" />
        )}
      </motion.button>

      <p className="text-xs text-white/60 text-center">
        {isListening ? 'Click to stop listening' : 'Click to start voice chat'}
      </p>
    </div>
  );
}

// Chat Mode Component
function ChatMode({ messages, inputText, setInputText, onSendMessage, isTyping, messagesEndRef }: {
  messages: Message[];
  inputText: string;
  setInputText: (text: string) => void;
  onSendMessage: (message: string) => void;
  isTyping: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement>;
}) {
  return (
    <>
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent min-h-0">
        {messages.length === 0 ? (
          <div className="text-center text-white/70 py-8">
            <div className="text-4xl mb-4">👋</div>
            <p className="text-lg font-semibold">Hi! I'm Saurabh Dahariya</p>
            <p className="text-sm mt-2 text-white/50">Ask me about my projects, skills, or experience!</p>
            <div className="flex flex-wrap justify-center gap-2 mt-4">
              <SuggestionButton text="Tell me about your projects" onSend={onSendMessage} />
              <SuggestionButton text="What are your skills?" onSend={onSendMessage} />
              <SuggestionButton text="How can I contact you?" onSend={onSendMessage} />
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[85%] p-3 rounded-2xl shadow-lg break-words overflow-hidden ${
                    message.isUser
                      ? 'bg-gradient-to-r from-primary to-blue-600 text-white rounded-br-md'
                      : 'bg-white/10 text-white border border-white/20 backdrop-blur-sm rounded-bl-md'
                  }`}
                >
                  <p className="text-sm leading-relaxed break-words whitespace-pre-wrap">{message.text}</p>
                  <p className="text-xs opacity-60 mt-1">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </p>
                </div>
              </motion.div>
            ))}

            {/* Typing indicator */}
            {isTyping && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex justify-start"
              >
                <div className="bg-white/10 border border-white/20 backdrop-blur-sm rounded-2xl rounded-bl-md p-3 max-w-[85%]">
                  <div className="flex items-center space-x-1">
                    <div className="text-white/70 text-sm">Saurabh is typing</div>
                    <div className="flex space-x-1">
                      {[0, 1, 2].map((i) => (
                        <motion.div
                          key={i}
                          className="w-1 h-1 bg-white/50 rounded-full"
                          animate={{ opacity: [0.3, 1, 0.3] }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            delay: i * 0.2
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Scroll anchor */}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t border-white/20 bg-black/20">
        <div className="flex gap-2">
          <Input
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                onSendMessage(inputText);
              }
            }}
            placeholder="Ask me anything about my work..."
            className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:bg-white/15 focus:border-white/40 transition-all"
            disabled={isTyping}
          />
          <Button
            size="sm"
            onClick={() => onSendMessage(inputText)}
            disabled={!inputText.trim() || isTyping}
            className="bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 transition-all"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-xs text-white/50 text-center mt-2">
          Try: "Tell me about your projects" • "What are your skills?" • "How can I contact you?"
        </p>
      </div>
    </>
  );
}

// Suggestion Button Component
function SuggestionButton({ text, onSend }: { text: string; onSend: (message: string) => void }) {
  return (
    <button
      onClick={() => onSend(text)}
      className="px-3 py-1 text-xs bg-white/10 hover:bg-white/20 rounded-full border border-white/20 transition-all"
    >
      {text}
    </button>
  );
}

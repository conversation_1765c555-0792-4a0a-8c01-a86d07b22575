"use client";

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON>, <PERSON>cO<PERSON>, MessageCircle, X, Send } from "lucide-react";

import { Input } from "@/components/ui/input";

type Mode = 'voice' | 'chat';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export function NewAIAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const [mode, setMode] = useState<Mode>('voice');
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [inputText, setInputText] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const currentAudioRef = useRef<HTMLAudioElement | null>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const messagesContainerRef = useRef<HTMLDivElement | null>(null);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      
      recognitionInstance.continuous = false;
      recognitionInstance.interimResults = false;
      recognitionInstance.lang = 'en-US';

      recognitionInstance.onstart = () => setIsListening(true);
      recognitionInstance.onend = () => setIsListening(false);
      
      recognitionInstance.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        handleVoiceInput(transcript);
      };

      recognitionInstance.onerror = (event) => {
        if (event.error !== 'no-speech' && event.error !== 'aborted' && event.error !== 'network') {
          console.error('Speech recognition error:', event.error);
        }
        setIsListening(false);
      };

      setRecognition(recognitionInstance);
    }
  }, []);

  // Don't auto-start - let user click to start

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  }, [messages]);

  // Scroll to bottom function
  const scrollToBottom = () => {
    console.log('Scrolling to bottom...');
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    setShowScrollButton(false);
  };

  // Handle scroll to show/hide scroll button
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;
    const isAtBottom = scrollHeight - scrollTop <= clientHeight + 50;

    console.log('Scroll Debug:', {
      scrollTop,
      scrollHeight,
      clientHeight,
      isAtBottom,
      messagesLength: messages.length
    });

    setShowScrollButton(!isAtBottom && messages.length > 2);
  };

  const startListening = () => {
    if (recognition && !isListening && !isSpeaking) {
      try {
        // Ensure recognition is stopped before starting
        recognition.abort();
        setTimeout(() => {
          if (!isListening && !isSpeaking && recognition) {
            recognition.start();
          }
        }, 100);
      } catch (error) {
        console.error('Error starting recognition:', error);
      }
    }
  };

  const stopListening = () => {
    if (recognition && isListening) {
      recognition.stop();
    }
  };

  // Handle navigation commands and clean response text
  const handleNavigation = (response: string) => {
    console.log('Checking navigation for response:', response);

    if (response.includes('NAVIGATE:/projects')) {
      console.log('Navigating to projects');
      setTimeout(() => {
        document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' });
      }, 500);
    } else if (response.includes('NAVIGATE:/about')) {
      console.log('Navigating to about');
      setTimeout(() => {
        document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' });
      }, 500);
    } else if (response.includes('NAVIGATE:/contact')) {
      console.log('Navigating to contact');
      setTimeout(() => {
        document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
      }, 500);
    } else if (response.includes('NAVIGATE:/skills')) {
      console.log('Navigating to skills');
      setTimeout(() => {
        document.getElementById('skills')?.scrollIntoView({ behavior: 'smooth' });
      }, 500);
    } else if (response.includes('NAVIGATE:/home')) {
      console.log('Navigating to home');
      setTimeout(() => {
        document.getElementById('home')?.scrollIntoView({ behavior: 'smooth' });
      }, 500);
    }
  };

  // Clean response text by removing navigation commands
  const cleanResponseText = (text: string): string => {
    return text
      .replace(/NAVIGATE:\/[a-zA-Z]+/g, '') // Remove all NAVIGATE commands
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim(); // Remove leading/trailing whitespace
  };

  const handleVoiceInput = async (transcript: string) => {
    console.log('Voice input:', transcript);

    // Stop any currently playing audio
    if (currentAudioRef.current) {
      currentAudioRef.current.pause();
      currentAudioRef.current = null;
    }
    setIsSpeaking(false);

    try {
      // Get AI response
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: transcript }),
      });

      const data = await response.json();

      if (data.response) {
        // Check for navigation commands
        handleNavigation(data.response);

        // Clean the response text for speaking (remove navigation commands)
        const cleanedResponse = cleanResponseText(data.response);

        // Only speak if there's actual content after cleaning
        if (cleanedResponse.trim()) {
          await speakResponse(cleanedResponse);
        }
      }
    } catch (error) {
      console.error('Error processing voice input:', error);
    }
  };

  const handleChatInput = async (message: string) => {
    if (!message.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: message,
      isUser: true,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    try {
      // Get AI response
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message }),
      });

      const data = await response.json();

      if (data.response) {
        // Check for navigation commands
        handleNavigation(data.response);

        // Clean the response text for display (remove navigation commands)
        const cleanedResponse = cleanResponseText(data.response);

        // Add AI response with cleaned text
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: cleanedResponse,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error processing chat input:', error);
      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: "Sorry, I'm having trouble responding right now. Please try again!",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
      // Force scroll to bottom after response
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 200);
    }
  };

  const speakResponse = async (text: string) => {
    try {
      // Stop any currently playing audio
      if (currentAudioRef.current) {
        currentAudioRef.current.pause();
        currentAudioRef.current = null;
      }

      setIsSpeaking(true);

      const response = await fetch('/api/voice', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text }),
      });

      const data = await response.json();

      if (data.success && data.audio) {
        // Play the audio
        const audio = new Audio(`data:audio/mpeg;base64,${data.audio}`);
        currentAudioRef.current = audio;

        audio.onended = () => {
          setIsSpeaking(false);
          currentAudioRef.current = null;
        };
        audio.onerror = () => {
          setIsSpeaking(false);
          currentAudioRef.current = null;
        };

        await audio.play();
      } else {
        setIsSpeaking(false);
      }
    } catch (error) {
      console.error('Error speaking response:', error);
      setIsSpeaking(false);
    }
  };

  const closeAssistant = () => {
    if (isListening && recognition) {
      recognition.stop();
    }
    if (currentAudioRef.current) {
      currentAudioRef.current.pause();
      currentAudioRef.current = null;
    }
    setIsSpeaking(false);
    setIsListening(false);
    setIsOpen(false);
    setMessages([]);
  };

  return (
    <>
      {/* Floating AI Button - Only show when chat is closed */}
      <AnimatePresence>
        {!isOpen && (
          <motion.button
            onClick={() => setIsOpen(true)}
            className="fixed bottom-6 right-6 z-50 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white rounded-full shadow-2xl hover:shadow-3xl transition-all group overflow-hidden"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            initial={{ opacity: 0, scale: 0, rotate: -180 }}
            animate={{ opacity: 1, scale: 1, rotate: 0 }}
            exit={{ opacity: 0, scale: 0, rotate: 180 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
          >
        {/* Animated background */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          animate={{ rotate: [0, 360] }}
          transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
        />

        {/* Content */}
        <div className="relative flex items-center gap-3 px-6 py-4">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="group-hover:animate-pulse"
          >
            <MessageCircle className="h-6 w-6" />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 2.5, duration: 0.8 }}
            className="flex flex-col items-start"
          >
            <motion.span
              className="font-bold text-lg tracking-wider"
              animate={{
                textShadow: [
                  "0 0 0px rgba(255,255,255,0)",
                  "0 0 10px rgba(255,255,255,0.8)",
                  "0 0 0px rgba(255,255,255,0)"
                ]
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              AI
            </motion.span>
            <motion.span
              className="text-xs opacity-90"
              initial={{ opacity: 0 }}
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              Assistant
            </motion.span>
          </motion.div>
        </div>

        {/* Pulse effect */}
        <motion.div
          className="absolute inset-0 rounded-full border-2 border-white/30"
          animate={{ scale: [1, 1.5], opacity: [0.5, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
          </motion.button>
        )}
      </AnimatePresence>

      {/* AI Assistant Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-30 pointer-events-none"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0, x: 100, y: 100 }}
              animate={{ scale: 1, opacity: 1, x: 0, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, x: 100, y: 100 }}
              className="fixed bottom-6 right-6 w-96 h-[600px] bg-black/90 backdrop-blur-2xl border border-white/10 rounded-3xl shadow-2xl flex flex-col pointer-events-auto overflow-hidden"
              style={{
                background: 'rgba(0, 0, 0, 0.85)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
              }}
            >
              {/* Persistent Glassmorphic Header - Never Disappears */}
              <div className="sticky top-0 z-50 border-b border-white/5 bg-black/20 backdrop-blur-3xl flex-shrink-0">
                {/* Header Content */}
                <div className="p-4">
                  {/* Top Row */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <div className="w-2.5 h-2.5 rounded-full bg-emerald-400 animate-pulse"></div>
                        <div className="absolute inset-0 w-2.5 h-2.5 rounded-full bg-emerald-400 animate-ping opacity-40"></div>
                      </div>
                      <div>
                        <h3 className="text-white/90 font-medium text-base">Saurabh AI</h3>
                        <p className="text-white/50 text-xs">Personal Assistant</p>
                      </div>
                    </div>
                    <motion.button
                      onClick={closeAssistant}
                      className="p-2 rounded-xl bg-white/5 hover:bg-red-500/10 text-white/60 hover:text-red-300 transition-all border border-white/5 backdrop-blur-sm"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <X className="h-4 w-4" />
                    </motion.button>
                  </div>

                  {/* Glassmorphic Mode Toggle */}
                  <div className="relative bg-white/5 rounded-2xl p-1 border border-white/10 backdrop-blur-sm">
                    <motion.div
                      className="absolute top-1 bottom-1 rounded-xl shadow-lg backdrop-blur-sm"
                      animate={{
                        left: mode === 'voice' ? '4px' : '50%',
                        right: mode === 'voice' ? '50%' : '4px',
                        background: mode === 'voice'
                          ? 'linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(29, 78, 216, 0.8))'
                          : 'linear-gradient(135deg, rgba(139, 92, 246, 0.8), rgba(124, 58, 237, 0.8))'
                      }}
                      transition={{ type: "spring", stiffness: 500, damping: 35 }}
                    />
                    <div className="relative flex">
                      <motion.button
                        onClick={() => {
                          if (mode !== 'voice') {
                            if (isListening) stopListening();
                            if (isSpeaking) {
                              if (currentAudioRef.current) {
                                currentAudioRef.current.pause();
                                currentAudioRef.current = null;
                              }
                              setIsSpeaking(false);
                            }
                            setMode('voice');
                            setMessages([]);
                          }
                        }}
                        className={`flex-1 flex items-center justify-center gap-2 py-2.5 px-3 rounded-xl text-sm font-medium transition-all relative z-10 ${
                          mode === 'voice' ? 'text-white' : 'text-white/60 hover:text-white/80'
                        }`}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Mic className="h-4 w-4" />
                        Voice
                      </motion.button>
                      <motion.button
                        onClick={() => {
                          if (mode !== 'chat') {
                            if (isListening) stopListening();
                            if (isSpeaking) {
                              if (currentAudioRef.current) {
                                currentAudioRef.current.pause();
                                currentAudioRef.current = null;
                              }
                              setIsSpeaking(false);
                            }
                            setMode('chat');
                          }
                        }}
                        className={`flex-1 flex items-center justify-center gap-2 py-2.5 px-3 rounded-xl text-sm font-medium transition-all relative z-10 ${
                          mode === 'chat' ? 'text-white' : 'text-white/60 hover:text-white/80'
                        }`}
                        whileTap={{ scale: 0.98 }}
                      >
                        <MessageCircle className="h-4 w-4" />
                        Chat
                      </motion.button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 flex flex-col">
                {mode === 'voice' ? (
                  <VoiceMode
                    isListening={isListening}
                    isSpeaking={isSpeaking}
                    onToggleListening={() => {
                      if (isListening) {
                        stopListening();
                      } else if (isSpeaking) {
                        // Stop speaking
                        if (currentAudioRef.current) {
                          currentAudioRef.current.pause();
                          currentAudioRef.current = null;
                        }
                        setIsSpeaking(false);
                      } else {
                        startListening();
                      }
                    }}
                  />
                ) : (
                  <ChatMode
                    messages={messages}
                    inputText={inputText}
                    setInputText={setInputText}
                    onSendMessage={handleChatInput}
                    isTyping={isTyping}
                    messagesEndRef={messagesEndRef}
                    messagesContainerRef={messagesContainerRef}
                    showScrollButton={showScrollButton}
                    scrollToBottom={scrollToBottom}
                    onScroll={handleScroll}
                  />
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

// Glassmorphic Voice Mode Component
function VoiceMode({ isListening, isSpeaking, onToggleListening }: {
  isListening: boolean;
  isSpeaking: boolean;
  onToggleListening: () => void;
}) {
  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8 space-y-8 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 backdrop-blur-sm">
      {/* Enhanced Glassmorphic Waveform */}
      <div className="flex items-center justify-center space-x-2 h-20 p-6 bg-white/5 rounded-3xl border border-white/10 backdrop-blur-xl">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className={`w-1.5 rounded-full transition-all duration-200 ${
              isListening
                ? 'bg-gradient-to-t from-red-500 to-pink-500'
                : isSpeaking
                ? 'bg-gradient-to-t from-blue-500 to-purple-500'
                : 'bg-gradient-to-t from-blue-500/30 to-purple-500/30'
            }`}
            animate={{
              height: isListening || isSpeaking ? [25, 50 + Math.random() * 25, 25] : 25,
              opacity: isListening || isSpeaking ? [0.8, 1, 0.8] : 0.4
            }}
            transition={{
              duration: 0.4 + Math.random() * 0.4,
              repeat: isListening || isSpeaking ? Infinity : 0,
              ease: "easeInOut",
              delay: i * 0.08
            }}
          />
        ))}
      </div>

      {/* Glassmorphic Status Card */}
      <div className="text-center space-y-4 p-6 bg-white/5 rounded-3xl border border-white/10 backdrop-blur-xl">
        <motion.h3 className="text-2xl font-semibold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
          {isListening ? '🎤 Listening...' : isSpeaking ? '🤖 AI Speaking...' : '💬 Ready to chat'}
        </motion.h3>
        <p className="text-sm text-white/80 max-w-xs mx-auto leading-relaxed">
          {isListening
            ? 'I\'m listening to your question. Speak clearly!'
            : isSpeaking
            ? 'AI is responding with voice...'
            : 'Click the button below to start or stop voice chat'
          }
        </p>
      </div>

      {/* Enhanced Glassmorphic Button */}
      <motion.button
        onClick={() => {
          if (isListening || isSpeaking) {
            if (isListening) onToggleListening();
            if (isSpeaking) {
              onToggleListening();
            }
          } else {
            onToggleListening();
          }
        }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className={`relative p-8 rounded-full text-white shadow-2xl transition-all duration-300 backdrop-blur-xl border-2 ${
          isListening
            ? 'bg-gradient-to-r from-red-500/80 to-pink-500/80 hover:from-red-500 hover:to-pink-500 border-red-400/30 shadow-red-500/25'
            : isSpeaking
            ? 'bg-gradient-to-r from-orange-500/80 to-yellow-500/80 hover:from-orange-500 hover:to-yellow-500 border-orange-400/30 shadow-orange-500/25'
            : 'bg-gradient-to-r from-blue-500/80 via-purple-500/80 to-pink-500/80 hover:from-blue-500 hover:via-purple-500 hover:to-pink-500 border-blue-400/30 shadow-blue-500/25'
        }`}
      >
        {(isListening || isSpeaking) && (
          <div className={`absolute inset-0 rounded-full animate-ping ${
            isListening
              ? 'bg-gradient-to-r from-red-500 to-pink-500'
              : isSpeaking
              ? 'bg-gradient-to-r from-orange-500 to-yellow-500'
              : 'bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500'
          } opacity-30`} />
        )}

        {isListening ? (
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
            className="relative z-10"
          >
            <MicOff className="h-10 w-10" />
          </motion.div>
        ) : isSpeaking ? (
          <motion.div
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="relative z-10"
          >
            <Mic className="h-10 w-10" />
          </motion.div>
        ) : (
          <Mic className="h-10 w-10 relative z-10" />
        )}
      </motion.button>

      <div className="text-center p-4 bg-white/5 rounded-2xl border border-white/10 backdrop-blur-xl">
        <p className="text-xs text-white/70">
          {isListening
            ? 'Click to stop listening'
            : isSpeaking
            ? 'Click to stop speaking'
            : 'Click to start voice chat'
          }
        </p>
      </div>
    </div>
  );
}

// Modern Glassmorphic Chat Mode Component
function ChatMode({
  messages,
  inputText,
  setInputText,
  onSendMessage,
  isTyping,
  messagesEndRef,
  messagesContainerRef,
  showScrollButton,
  scrollToBottom,
  onScroll
}: {
  messages: Message[];
  inputText: string;
  setInputText: (text: string) => void;
  onSendMessage: (message: string) => void;
  isTyping: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
  messagesContainerRef: React.RefObject<HTMLDivElement | null>;
  showScrollButton: boolean;
  scrollToBottom: () => void;
  onScroll: (e: React.UIEvent<HTMLDivElement>) => void;
}) {
  return (
    <div className="flex-1 flex flex-col relative bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 backdrop-blur-sm">
      {/* Glassmorphic Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto overflow-x-hidden px-5 py-4 space-y-4 custom-scrollbar"
        style={{
          minHeight: 0,
          maxHeight: 'calc(100% - 100px)',
          scrollBehavior: 'smooth'
        }}
        onScroll={onScroll}
      >
        {messages.length === 0 ? (
          <div className="text-center text-white/70 py-8 px-4">
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
              className="space-y-6"
            >
              <div className="text-4xl">👋</div>
              <div className="space-y-2">
                <h3 className="text-xl font-semibold text-white/90">Hi! I'm Saurabh</h3>
                <p className="text-sm text-white/60">Ask me about my projects, skills, or experience!</p>
              </div>
              <div className="flex flex-wrap justify-center gap-2">
                <SuggestionButton text="Tell me about your projects" onSend={onSendMessage} />
                <SuggestionButton text="What are your skills?" onSend={onSendMessage} />
                <SuggestionButton text="How can I contact you?" onSend={onSendMessage} />
              </div>
            </motion.div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.4, ease: "easeOut" }}
                className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[85%] p-4 rounded-2xl shadow-lg break-words backdrop-blur-xl ${
                    message.isUser
                      ? 'bg-gradient-to-br from-blue-500/80 via-purple-500/80 to-pink-500/80 text-white rounded-br-md border border-blue-400/30'
                      : 'bg-white/10 text-white border border-white/20 rounded-bl-md'
                  }`}
                >
                  <p className="text-sm leading-relaxed break-words whitespace-pre-wrap">{message.text}</p>
                  <p className="text-xs opacity-60 mt-2 text-right">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </p>
                </div>
              </motion.div>
            ))}

            {/* Enhanced Typing Indicator */}
            {isTyping && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="flex justify-start"
              >
                <div className="bg-white/10 border border-white/20 backdrop-blur-xl rounded-2xl rounded-bl-md p-4 max-w-[85%]">
                  <div className="flex items-center space-x-3">
                    <div className="text-white/80 text-sm font-medium">Saurabh is typing</div>
                    <div className="flex space-x-1">
                      {[0, 1, 2].map((i) => (
                        <motion.div
                          key={i}
                          className="w-2 h-2 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full"
                          animate={{
                            scale: [1, 1.4, 1],
                            opacity: [0.4, 1, 0.4]
                          }}
                          transition={{
                            duration: 1.2,
                            repeat: Infinity,
                            delay: i * 0.15,
                            ease: "easeInOut"
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Scroll anchor */}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Enhanced Scroll Button */}
      <AnimatePresence>
        {showScrollButton && (
          <motion.button
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            onClick={scrollToBottom}
            className="absolute bottom-24 right-4 p-3 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 text-white rounded-full shadow-xl backdrop-blur-xl border border-white/20"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <motion.div
              animate={{ y: [0, 3, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7 14l5 5 5-5z"/>
              </svg>
            </motion.div>
          </motion.button>
        )}
      </AnimatePresence>

      {/* Enhanced Glassmorphic Input Area */}
      <div className="p-4 border-t border-white/5 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5 backdrop-blur-2xl flex-shrink-0">
        <div className="relative">
          <Input
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                onSendMessage(inputText);
              }
            }}
            placeholder="Type your message..."
            className="w-full bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:bg-white/15 focus:border-blue-400/50 transition-all rounded-2xl pr-14 py-3 text-sm backdrop-blur-xl"
            disabled={isTyping}
          />
          <motion.button
            onClick={() => onSendMessage(inputText)}
            disabled={!inputText.trim() || isTyping}
            className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 disabled:from-white/20 disabled:to-white/20 disabled:cursor-not-allowed text-white rounded-xl transition-all shadow-lg backdrop-blur-sm"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Send className="h-4 w-4" />
          </motion.button>
        </div>
        <p className="text-xs text-white/50 text-center mt-2">
          Press Enter to send • Shift+Enter for new line
        </p>
      </div>
    </div>
  );
}

// Enhanced Glassmorphic Suggestion Button Component
function SuggestionButton({ text, onSend }: { text: string; onSend: (message: string) => void }) {
  return (
    <motion.button
      onClick={() => onSend(text)}
      className="px-4 py-2 text-sm bg-white/10 hover:bg-gradient-to-r hover:from-blue-500/20 hover:via-purple-500/20 hover:to-pink-500/20 rounded-xl border border-white/20 hover:border-blue-400/40 transition-all text-white/80 hover:text-white backdrop-blur-xl"
      whileHover={{ scale: 1.05, y: -1 }}
      whileTap={{ scale: 0.95 }}
    >
      {text}
    </motion.button>
  );
}

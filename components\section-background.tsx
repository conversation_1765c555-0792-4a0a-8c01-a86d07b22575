"use client";

interface SectionBackgroundProps {
  variant?: 'default' | 'alternate' | 'dark';
  children: React.ReactNode;
  className?: string;
}

export function SectionBackground({ 
  variant = 'default', 
  children, 
  className = '' 
}: SectionBackgroundProps) {
  const getBackgroundClass = () => {
    switch (variant) {
      case 'alternate':
        return 'bg-gradient-to-br from-muted/30 to-background relative';
      case 'dark':
        return 'bg-gradient-to-br from-background to-muted/20 relative';
      default:
        return 'bg-gradient-to-br from-background to-background/95 relative';
    }
  };

  const getOverlayPattern = () => {
    switch (variant) {
      case 'alternate':
        return (
          <>
            <div className="absolute top-10 right-10 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
            <div className="absolute bottom-10 left-10 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl"></div>
          </>
        );
      case 'dark':
        return (
          <>
            <div className="absolute top-20 left-20 w-72 h-72 bg-primary/8 rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 right-20 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
          </>
        );
      default:
        return (
          <>
            <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/3 rounded-full blur-3xl"></div>
            <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500/3 rounded-full blur-3xl"></div>
          </>
        );
    }
  };

  return (
    <section className={`${getBackgroundClass()} ${className}`}>
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {getOverlayPattern()}
        
        {/* Grid Pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-background/5 to-transparent"></div>
      </div>
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </section>
  );
}

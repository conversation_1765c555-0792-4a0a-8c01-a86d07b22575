import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { TechBackground3D } from "@/components/TechBackground3D";
import { GSAPProvider } from "@/components/gsap-provider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Saurabh - Full Stack Developer",
  description: "Port<PERSON><PERSON> of Saurabh, a passionate full-stack web developer specializing in modern web technologies. Explore my projects, skills, and experience.",
  keywords: ["Full Stack Developer", "Web Developer", "React", "Next.js", "Portfolio", "<PERSON><PERSON><PERSON><PERSON>"],
  authors: [{ name: "<PERSON><PERSON><PERSON><PERSON>" }],
  creator: "<PERSON><PERSON><PERSON><PERSON>",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://saurabh-portfolio.vercel.app",
    title: "Saurabh - Full Stack Developer",
    description: "Portfolio of Saurabh, a passionate full-stack web developer specializing in modern web technologies.",
    siteName: "Saurabh Portfolio",
  },
  twitter: {
    card: "summary_large_image",
    title: "Saurabh - Full Stack Developer",
    description: "Portfolio of Saurabh, a passionate full-stack web developer specializing in modern web technologies.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-background font-sans`}
        suppressHydrationWarning
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <GSAPProvider>
            <TechBackground3D />
            {children}
            <Toaster />
          </GSAPProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}


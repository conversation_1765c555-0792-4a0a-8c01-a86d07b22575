import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { GSAPProvider } from "@/components/gsap-provider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Saurabh - Full Stack Developer",
  description: "Portfolio of <PERSON><PERSON><PERSON><PERSON>, a passionate full-stack web developer specializing in modern web technologies. Explore my projects, skills, and experience.",
  keywords: ["Full Stack Developer", "Web Developer", "React", "Next.js", "Portfolio", "Saurabh"],
  authors: [{ name: "<PERSON><PERSON><PERSON><PERSON>" }],
  creator: "<PERSON><PERSON><PERSON><PERSON>",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://saurabh-portfolio.vercel.app",
    title: "Saurabh - Full Stack Developer",
    description: "Portfolio of Saurabh, a passionate full-stack web developer specializing in modern web technologies.",
    siteName: "Saurabh Portfolio",
  },
  twitter: {
    card: "summary_large_image",
    title: "Saurabh - Full Stack Developer",
    description: "Portfolio of Saurabh, a passionate full-stack web developer specializing in modern web technologies.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen font-sans`}
        suppressHydrationWarning
      >
        {/* Global Background */}
        <div className="fixed inset-0 -z-50">
          {/* Base gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20"></div>

          {/* Animated gradient orbs */}
          <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-primary/10 to-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-cyan-500/10 to-teal-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>

          {/* Grid pattern */}
          <div className="absolute inset-0 bg-grid-pattern opacity-30"></div>
        </div>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem={false}
          disableTransitionOnChange
        >
          <GSAPProvider>
            {children}
            <Toaster />
          </GSAPProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}


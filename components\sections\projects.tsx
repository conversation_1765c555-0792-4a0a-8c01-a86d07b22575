"use client";

import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Github } from "lucide-react";
import Image from "next/image";
import { useEffect, useRef } from "react";
import { useGSAP } from "@/hooks/useGSAP";

export default function ProjectsSection() {
  const { fadeIn, slideIn, staggerAnimation } = useGSAP();
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const projectsRef = useRef<HTMLDivElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  const projects = [
    {
      title: "QuizByAI",
      description: "An AI-powered quiz platform where users can generate quizzes using OpenAI based on topic, difficulty, and number of questions. Includes user login, custom quizzes, tracking, and role-based access.",
      image: "/quizbyai.png",
      video: "/quizbyai.mp4",
      technologies: ["ReactJS", "Firebase", "OpenAI API", "Tailwind CSS", "ShadCN UI"],
      githubUrl: "https://github.com/saurabhdahariya/quizbyai",
      liveUrl: "https://quizbyai.vercel.app",
      featured: true
    },
    {
      title: "NaturalSQL (Text-to-SQL)",
      description: "An AI-powered tool that lets users query databases using plain English. Converts natural language to SQL using OpenAI, offers a Try Demo with live sample data, and allows users to connect their own MySQL or PostgreSQL databases.",
      image: "/texttosql.png",
      video: "/texttosql.mp4",
      technologies: ["ReactJS", "Node.js", "OpenAI API", "Tailwind CSS", "ShadCN UI"],
      githubUrl: "https://github.com/saurabhdahariya/text-to-query-ai",
      liveUrl: "https://text-to-query-ai.onrender.com/",
      featured: true
    },
    {
      title: "Route Tracker",
      description: "A real-time location tracking app built with React and Redux that displays live routes on an interactive map. Uses Google Maps API for map integration and tracks user movement in real-time.",
      image: "/route-tracker.png",
      video: "/route-tracker.mp4",
      technologies: ["ReactJS", "Redux", "Google Maps API", "Tailwind CSS"],
      githubUrl: "https://github.com/saurabhdahariya/route-tracker",
      liveUrl: "https://saurabhd.vercel.app/map",
      featured: false
    }
  ];

  useEffect(() => {
    // GSAP animations for projects section
    if (titleRef.current) {
      fadeIn(titleRef, {
        trigger: sectionRef.current,
        start: "top 70%",
        duration: 1,
        from: { opacity: 0, y: 50 }
      });
    }

    if (projectsRef.current) {
      staggerAnimation(projectsRef.current.children, {
        trigger: projectsRef.current,
        start: "top 80%",
        delay: 0.3,
        stagger: 0.2,
        duration: 1,
        from: { opacity: 0, y: 60, scale: 0.9 },
        to: { opacity: 1, y: 0, scale: 1 }
      });
    }

    if (ctaRef.current) {
      slideIn(ctaRef, 'up', {
        trigger: ctaRef.current,
        start: "top 85%",
        delay: 0.5,
        duration: 0.8
      });
    }

    // Cleanup function
    return () => {
      // No cleanup needed for simple hover effects
    };
  }, []); // Empty dependency array since we only want this to run once

  return (
    <section ref={sectionRef} id="projects" className="py-20">
      <div className="container mx-auto px-4">
        <div
          ref={titleRef}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">Featured Projects</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            A showcase of my recent work and personal projects
          </p>
        </div>

        <div ref={projectsRef} className="grid gap-8">
          {projects.map((project, index) => (
            <div
              key={project.title}
              className="project-card group"
            >
              <Card className={`overflow-hidden transition-all duration-300 hover:shadow-2xl hover:shadow-primary/10 hover:border-primary/30 ${
                project.featured ? 'border-primary/20 shadow-lg' : ''
              }`}>
                <div className={`grid ${project.featured ? 'lg:grid-cols-2' : 'md:grid-cols-2'} gap-0`}>
                  {/* Project Preview */}
                  <div className="relative overflow-hidden bg-muted group">
                    <div className="aspect-video relative">
                      {/* Thumbnail image */}
                      <img
                        src={project.image}
                        alt={project.title}
                        className="absolute inset-0 w-full h-full object-cover transition-opacity duration-300"
                      />

                      {/* Video preview on hover (if available) */}
                      {project.video && (
                        <video
                          className="absolute inset-0 w-full h-full object-cover opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                          autoPlay
                          muted
                          loop
                          playsInline
                        >
                          <source src={project.video} type="video/mp4" />
                        </video>
                      )}

                      {/* Overlay gradient */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    {project.featured && (
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-primary text-primary-foreground">Featured</Badge>
                      </div>
                    )}
                  </div>

                  {/* Project Content */}
                  <div className="p-6 flex flex-col justify-between">
                    <div>
                      <CardHeader className="p-0 mb-3">
                        <CardTitle className="text-xl mb-2 group-hover:text-primary transition-colors">
                          {project.title}
                        </CardTitle>
                        <CardDescription className="text-sm leading-relaxed line-clamp-3">
                          {project.description}
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="p-0">
                        {/* Technologies */}
                        <div className="mb-4">
                          <div className="flex flex-wrap gap-1">
                            {project.technologies.slice(0, 4).map((tech) => (
                              <Badge key={tech} variant="outline" className="text-xs px-2 py-1">
                                {tech}
                              </Badge>
                            ))}
                            {project.technologies.length > 4 && (
                              <Badge variant="outline" className="text-xs px-2 py-1">
                                +{project.technologies.length - 4}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <a
                        href={project.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 border border-input bg-background hover:bg-accent hover:text-accent-foreground px-3 py-2 rounded-md text-xs font-medium transition-colors flex items-center justify-center gap-1"
                      >
                        <Github className="h-3 w-3" />
                        <span>Code</span>
                      </a>
                      <a
                        href={project.liveUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-2 rounded-md text-xs font-medium transition-colors flex items-center justify-center gap-1"
                      >
                        <ExternalLink className="h-3 w-3" />
                        <span>Live</span>
                      </a>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          ))}
        </div>

        {/* View More Projects */}
        <div
          ref={ctaRef}
          className="text-center mt-12"
        >
          <a
            href="https://github.com/saurabhdahariya"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground px-8 py-3 rounded-md font-medium transition-colors"
          >
            View All Projects on GitHub
            <ExternalLink className="h-4 w-4" />
          </a>
        </div>
      </div>
    </section>
  );
}

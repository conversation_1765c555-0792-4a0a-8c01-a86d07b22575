"use client";

import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Github } from "lucide-react";
import Image from "next/image";
import { useEffect, useRef } from "react";
import { useGSAP } from "@/hooks/useGSAP";
import { magneticHover } from "@/lib/gsap-animations";

export default function ProjectsSection() {
  const { fadeIn, slideIn, staggerAnimation } = useGSAP();
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const projectsRef = useRef<HTMLDivElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  const projects = [
    {
      title: "E-Commerce Platform",
      description: "A full-stack e-commerce solution with modern UI, secure payments, and admin dashboard. Built with Next.js, TypeScript, and Stripe integration.",
      image: "/api/placeholder/600/400",
      technologies: ["Next.js", "TypeScript", "Stripe", "Prisma", "PostgreSQL"],
      githubUrl: "https://github.com",
      liveUrl: "https://example.com",
      featured: true
    },
    {
      title: "Task Management App",
      description: "A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",
      image: "/api/placeholder/600/400",
      technologies: ["React", "Node.js", "Socket.io", "MongoDB", "Express"],
      githubUrl: "https://github.com",
      liveUrl: "https://example.com",
      featured: true
    },
    {
      title: "Weather Dashboard",
      description: "A responsive weather application with location-based forecasts, interactive maps, and detailed weather analytics.",
      image: "/api/placeholder/600/400",
      technologies: ["React", "OpenWeather API", "Chart.js", "Tailwind CSS"],
      githubUrl: "https://github.com",
      liveUrl: "https://example.com",
      featured: false
    },
    {
      title: "Portfolio Website",
      description: "A modern, responsive portfolio website with smooth animations, dark mode support, and optimized performance.",
      image: "/api/placeholder/600/400",
      technologies: ["Next.js", "Framer Motion", "GSAP", "Tailwind CSS", "TypeScript"],
      githubUrl: "https://github.com",
      liveUrl: "https://example.com",
      featured: false
    }
  ];

  useEffect(() => {
    // GSAP animations for projects section
    if (titleRef.current) {
      fadeIn(titleRef, {
        trigger: sectionRef.current,
        start: "top 70%",
        duration: 1,
        from: { opacity: 0, y: 50 }
      });
    }

    if (projectsRef.current) {
      staggerAnimation(projectsRef.current.children, {
        trigger: projectsRef.current,
        start: "top 80%",
        delay: 0.3,
        stagger: 0.2,
        duration: 1,
        from: { opacity: 0, y: 60, scale: 0.9 },
        to: { opacity: 1, y: 0, scale: 1 }
      });
    }

    if (ctaRef.current) {
      slideIn(ctaRef, 'up', {
        trigger: ctaRef.current,
        start: "top 85%",
        delay: 0.5,
        duration: 0.8
      });
    }

    // Add magnetic hover effect to project cards
    const projectCards = projectsRef.current?.querySelectorAll('.project-card');
    const cleanupFunctions: (() => void)[] = [];

    projectCards?.forEach(card => {
      const cleanup = magneticHover(card, 0.1);
      if (cleanup) cleanupFunctions.push(cleanup);
    });

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, []); // Empty dependency array since we only want this to run once

  return (
    <section ref={sectionRef} id="projects" className="py-20">
      <div className="container mx-auto px-4">
        <div
          ref={titleRef}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">Featured Projects</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            A showcase of my recent work and personal projects
          </p>
        </div>

        <div ref={projectsRef} className="grid gap-8">
          {projects.map((project, index) => (
            <div
              key={project.title}
              className="project-card"
            >
              <Card className={`overflow-hidden hover:shadow-xl transition-shadow duration-300 ${
                project.featured ? 'border-primary/20 shadow-lg' : ''
              }`}>
                <div className={`grid ${project.featured ? 'lg:grid-cols-2' : 'md:grid-cols-2'} gap-0`}>
                  {/* Project Image */}
                  <div className="relative overflow-hidden bg-muted">
                    <div className="aspect-video relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                        <span className="text-6xl opacity-50">🚀</span>
                      </div>
                    </div>
                    {project.featured && (
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-primary text-primary-foreground">Featured</Badge>
                      </div>
                    )}
                  </div>

                  {/* Project Content */}
                  <div className="p-6 flex flex-col justify-between">
                    <div>
                      <CardHeader className="p-0 mb-4">
                        <CardTitle className="text-xl mb-2">{project.title}</CardTitle>
                        <CardDescription className="text-base leading-relaxed">
                          {project.description}
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="p-0">
                        {/* Technologies */}
                        <div className="mb-6">
                          <div className="flex flex-wrap gap-2">
                            {project.technologies.map((tech) => (
                              <Badge key={tech} variant="outline" className="text-xs">
                                {tech}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-3">
                      <a
                        href={project.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 border border-input bg-background hover:bg-accent hover:text-accent-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center justify-center gap-2"
                      >
                        <span>GitHub</span>
                        <ExternalLink className="h-3 w-3" />
                      </a>
                      <a
                        href={project.liveUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center justify-center gap-2"
                      >
                        <span>Live Demo</span>
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          ))}
        </div>

        {/* View More Projects */}
        <div
          ref={ctaRef}
          className="text-center mt-12"
        >
          <a
            href="https://github.com"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground px-8 py-3 rounded-md font-medium transition-colors"
          >
            View All Projects on GitHub
            <ExternalLink className="h-4 w-4" />
          </a>
        </div>
      </div>
    </section>
  );
}

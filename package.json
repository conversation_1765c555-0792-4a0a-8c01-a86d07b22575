{"name": "my-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.14", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@types/three": "^0.176.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.12.2", "lottie-react": "^2.4.1", "lucide-react": "^0.511.0", "next": "15.1.8", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.1.8", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5"}}